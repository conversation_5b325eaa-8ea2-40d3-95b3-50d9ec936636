/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'placehold.co', // Placeholder images
            },
            {
                protocol: 'https',
                hostname: '*.amazonaws.com', // AWS images
            },
            {
                protocol: 'https',
                hostname: '*.cloudfront.net', // CloudFront images
            },
            {
                protocol: 'https',
                hostname: '*.supabase.co', // Supabase images
            },
            {
                protocol: 'https',
                hostname: 'rkjcixumtesncutclmxm.supabase.co', // Specific Supabase instance
            },
            {
                protocol: 'https',
                hostname: 'rkjcixumtesncutclmxm.supabase.co', // Allow images from this specific path
                pathname: '/storage/v1/object/sign/**', // Allow all images from the sign endpoint
            },
            {
                protocol: 'https',
                hostname: 'images.samsung.com', // Samsung images
            },
            {
                protocol: 'https',
                hostname: 'supabase.com', // Supabase logo and assets
            }
        ],
        // Enhanced image optimization configuration
        formats: ['image/webp', 'image/avif'], // Modern formats with fallbacks
        deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840], // Responsive breakpoints
        imageSizes: [16, 32, 48, 64, 96, 128, 256, 384], // Icon and thumbnail sizes
        minimumCacheTTL: 60 * 60 * 24 * 365, // Cache for 1 year
        dangerouslyAllowSVG: true, // Allow SVG images (for logos)
        contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;", // SVG security
        unoptimized: false, // Enable optimization for better performance
    },
    eslint: {
        ignoreDuringBuilds: true // Ignore ESLint warnings during builds
    },
    poweredByHeader: false, // Disable the X-Powered-By header
    compress: true, // Enable gzip compression
    productionBrowserSourceMaps: false, // Disable source maps in production

    // Enhanced performance optimizations
    // Note: swcMinify is enabled by default in Next.js 13+

    // Experimental features for better performance
    experimental: {
        optimizeCss: true, // Optimize CSS loading
        optimizePackageImports: ['lucide-react', 'framer-motion'], // Tree shake large packages
        turbo: {
            rules: {
                '*.svg': {
                    loaders: ['@svgr/webpack'],
                    as: '*.js',
                },
            },
        },
    },

    // Webpack optimizations
    webpack: (config, { dev, isServer }) => {
        // Production optimizations
        if (!dev && !isServer) {
            // Enable aggressive splitting for better caching
            config.optimization.splitChunks = {
                chunks: 'all',
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                        priority: 10,
                    },
                    common: {
                        name: 'common',
                        minChunks: 2,
                        chunks: 'all',
                        priority: 5,
                        reuseExistingChunk: true,
                    },
                },
            };
        }

        return config;
    },

    compiler: {
        removeConsole: process.env.NODE_ENV === 'production', // Remove console logs in production
    },

    // Headers for better caching and security
    async headers() {
        return [
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'X-DNS-Prefetch-Control',
                        value: 'on'
                    },
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY'
                    },
                ],
            },
            {
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, s-maxage=1800, stale-while-revalidate=3600'
                    },
                ],
            },
            {
                source: '/test-api-routes',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            },
            {
                source: '/test-data-layer',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            },
            {
                source: '/test-featured-debug',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            },
            {
                source: '/test-data-simple',
                headers: [
                    {
                        key: 'X-Robots-Tag',
                        value: 'noindex, nofollow'
                    }
                ],
            }
        ];
    },
}

module.exports = nextConfig;
