'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, SlidersHorizontal, ArrowLeft, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { ProductCard } from '@/components/ProductCard';
import { TransformedProduct } from '@/lib/data/types';

interface SearchPageClientProps {
    products: TransformedProduct[];
    totalCount: number;
    currentPage: number;
    searchQuery: string;
    category?: string;
    subcategory?: string;
}

type SortOption = 'recommended' | 'price_asc' | 'price_desc' | 'cashback_desc' | 'cashback_asc' | 'newest';

const sortOptions: Record<SortOption, string> = {
    recommended: 'Recommended',
    price_asc: 'Price: Low to High',
    price_desc: 'Price: High to Low',
    cashback_desc: 'Cashback: High to Low',
    cashback_asc: 'Cashback: Low to High',
    newest: 'Newest First',
};

export function SearchPageClient({
    products,
    totalCount,
    currentPage,
    searchQuery,
    category,
    subcategory
}: SearchPageClientProps) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [localSearch, setLocalSearch] = useState(searchQuery);
    const [selectedSort, setSelectedSort] = useState<SortOption>('recommended');
    const [showFilters, setShowFilters] = useState(false);

    const totalPages = Math.ceil(totalCount / 20); // 20 products per page

    // Sort products client-side for immediate feedback
    const sortedProducts = useMemo(() => {
        return [...products].sort((a, b) => {
            switch (selectedSort) {
                case 'price_asc':
                    const aMinPrice = Math.min(...(a.retailerOffers?.map(o => o.price) || [0]));
                    const bMinPrice = Math.min(...(b.retailerOffers?.map(o => o.price) || [0]));
                    return aMinPrice - bMinPrice;
                case 'price_desc':
                    const aMaxPrice = Math.max(...(a.retailerOffers?.map(o => o.price) || [0]));
                    const bMaxPrice = Math.max(...(b.retailerOffers?.map(o => o.price) || [0]));
                    return bMaxPrice - aMaxPrice;
                case 'cashback_desc':
                    return (b.cashbackAmount || 0) - (a.cashbackAmount || 0);
                case 'cashback_asc':
                    return (a.cashbackAmount || 0) - (b.cashbackAmount || 0);
                case 'newest':
                    return new Date(b.updatedAt || 0).getTime() - new Date(a.updatedAt || 0).getTime();
                case 'recommended':
                default:
                    // Recommended: prioritize featured products and higher cashback
                    const aScore = (a.isFeatured ? 100 : 0) + (a.cashbackAmount || 0);
                    const bScore = (b.isFeatured ? 100 : 0) + (b.cashbackAmount || 0);
                    return bScore - aScore;
            }
        });
    }, [products, selectedSort]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        const params = new URLSearchParams(searchParams);

        if (localSearch.trim()) {
            params.set('q', localSearch.trim());
        } else {
            params.delete('q');
        }
        params.delete('page'); // Reset to first page on new search

        router.push(`/search?${params.toString()}`);
    };

    const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSelectedSort(e.target.value as SortOption);
    };

    const handlePageChange = (page: number) => {
        const params = new URLSearchParams(searchParams);
        params.set('page', page.toString());
        router.push(`/search?${params.toString()}`);
    };

    const clearFilters = () => {
        router.push('/search');
    };

    return (
        <div className="container py-12">
            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
            >
                <h1 className="text-4xl font-bold text-primary mb-4">
                    {searchQuery ? `Search Results for "${searchQuery}"` : 'Search Products'}
                </h1>
                {(category || subcategory) && (
                    <div className="flex items-center gap-2 text-sm text-foreground/70 mb-4">
                        <span>Filtered by:</span>
                        {category && (
                            <span className="bg-primary/10 text-primary px-2 py-1 rounded">
                                {category}
                            </span>
                        )}
                        {subcategory && (
                            <span className="bg-secondary/10 text-secondary px-2 py-1 rounded">
                                {subcategory}
                            </span>
                        )}
                        <button
                            onClick={clearFilters}
                            className="text-primary hover:text-primary/80 underline"
                        >
                            Clear filters
                        </button>
                    </div>
                )}
            </motion.div>

            {/* Search Bar */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="mb-8"
            >
                <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
                    <div className="relative">
                        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-foreground/40" />
                        <input
                            type="text"
                            placeholder="Search for products, brands, or categories..."
                            value={localSearch}
                            onChange={(e) => setLocalSearch(e.target.value)}
                            className="w-full pl-12 pr-4 py-4 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-lg"
                        />
                        <button
                            type="submit"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                        >
                            Search
                        </button>
                    </div>
                </form>
            </motion.div>

            {/* Controls */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="flex justify-between items-center mb-8"
            >
                <div className="flex items-center gap-4">
                    <span className="text-foreground/70">
                        {totalCount.toLocaleString()} result{totalCount !== 1 ? 's' : ''} found
                    </span>
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-foreground/70 bg-secondary/10 rounded-lg hover:bg-secondary/20 transition-colors"
                    >
                        <SlidersHorizontal className="h-4 w-4" />
                        Filters
                    </button>
                </div>

                <div className="flex items-center gap-2">
                    <label htmlFor="sort" className="text-sm text-foreground/70">
                        Sort by:
                    </label>
                    <select
                        id="sort"
                        value={selectedSort}
                        onChange={handleSortChange}
                        className="px-4 py-2 text-sm font-medium text-foreground bg-white border border-border rounded-lg hover:bg-secondary/5 focus:outline-none focus:ring-2 focus:ring-primary/20"
                    >
                        {Object.entries(sortOptions).map(([value, label]) => (
                            <option key={value} value={value}>
                                {label}
                            </option>
                        ))}
                    </select>
                </div>
            </motion.div>

            {/* Results */}
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
            >
                {sortedProducts.length > 0 ? (
                    <>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                            {sortedProducts.map((product, index) => (
                                <motion.div
                                    key={product.id}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.05 }}
                                >
                                    <ProductCard product={product} />
                                </motion.div>
                            ))}
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="flex justify-center">
                                <div className="flex gap-2">
                                    {/* Previous button */}
                                    {currentPage > 1 && (
                                        <button
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            className="flex items-center gap-2 px-4 py-2 rounded-lg border border-border hover:bg-secondary/10 transition-colors"
                                        >
                                            <ArrowLeft className="h-4 w-4" />
                                            Previous
                                        </button>
                                    )}

                                    {/* Page numbers */}
                                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                        const pageNum = Math.max(1, currentPage - 2) + i;
                                        if (pageNum > totalPages) return null;

                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                className={`px-4 py-2 rounded-lg transition-colors ${pageNum === currentPage
                                                    ? 'bg-primary text-primary-foreground'
                                                    : 'border border-border hover:bg-secondary/10'
                                                    }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}

                                    {/* Next button */}
                                    {currentPage < totalPages && (
                                        <button
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            className="flex items-center gap-2 px-4 py-2 rounded-lg border border-border hover:bg-secondary/10 transition-colors"
                                        >
                                            Next
                                            <ArrowRight className="h-4 w-4" />
                                        </button>
                                    )}
                                </div>
                            </div>
                        )}
                    </>
                ) : (
                    <div className="text-center py-12">
                        <Search className="h-16 w-16 text-foreground/20 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-foreground/70 mb-2">
                            No products found
                        </h3>
                        <p className="text-foreground/60 mb-6">
                            Try adjusting your search terms or browse our categories.
                        </p>
                        <Link
                            href="/products"
                            className="inline-flex items-center gap-2 text-primary hover:text-primary/90 font-medium"
                        >
                            Browse All Products
                        </Link>
                    </div>
                )}
            </motion.div>
        </div>
    );
}
