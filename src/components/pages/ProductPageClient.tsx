'use client';

import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { ProductInfo } from '../../app/products/components/ProductInfo';
import { PriceComparison } from '../../app/products/components/PriceComparison';
import { SimilarProducts } from '../../app/products/components/SimilarProducts';
import { ProductDetailsSection } from '../../app/products/components/ProductDetailsSection';
import { TransformedProduct } from '@/lib/data/types';

interface ProductPageClientProps {
    product: TransformedProduct;
    similarProducts: TransformedProduct[];
    returnTo?: string;
}

export function ProductPageClient({ product, similarProducts, returnTo }: ProductPageClientProps) {
    // Decode the return URL or fallback to products page
    const backUrl = returnTo ? decodeURIComponent(returnTo) : '/products';
    return (
        <div className="container py-12">
            <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="max-w-4xl mx-auto"
            >
                <Link
                    href={backUrl}
                    className="inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/90 mb-8"
                >
                    <ArrowLeft className="h-4 w-4" /> Back to Products
                </Link>

                <ProductInfo product={product} />

                {/* Product Details Section */}
                <ProductDetailsSection
                    description={product.description || ''}
                    specifications={product.specifications || null}
                />

                {/* Price Comparison Section */}

                {product.retailerOffers && product.retailerOffers.length > 0 && (
                    <PriceComparison
                        retailerOffers={product.retailerOffers.map(offer => ({
                            ...offer,
                            cashback: product.cashbackAmount ?? 0,
                            stockStatus: offer.stockStatus || 'unknown', // Provide a default value
                            url: offer.url || '', // Provide a default empty string if null
                            retailer: {
                                ...offer.retailer,
                                logoUrl: offer.retailer.logoUrl || '',
                                websiteUrl: offer.retailer.websiteUrl || ''
                            }
                        }))}
                    />
                )}

                {/* Similar Products Section */}
                {similarProducts && similarProducts.length > 0 && (
                    <SimilarProducts products={similarProducts} />
                )}
            </motion.div>
        </div>
    );
}
