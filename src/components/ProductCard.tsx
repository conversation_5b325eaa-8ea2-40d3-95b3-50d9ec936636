import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';

// Import the TransformedRetailerOffer type from the data layer
import type { TransformedRetailerOffer } from '@/lib/data/types';

// Use the TransformedRetailerOffer type
type RetailerOffer = TransformedRetailerOffer;

// Import the TransformedProduct type from the data layer
import type { TransformedProduct } from '@/lib/data/types';

// Use the TransformedProduct type directly
type Product = TransformedProduct;

interface ProductCardProps {
	product: Product;
}

function getTimeLeft(validUntil: string): string {
	const now = new Date();
	const expiryDate = new Date(validUntil);
	const timeLeft = expiryDate.getTime() - now.getTime();

	const days = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));

	if (days > 0) {
		return `${days} day${days !== 1 ? 's' : ''} left`;
	} else {
		return 'Expiring soon';
	}
}



export function ProductCard({ product }: ProductCardProps) {

	const [currentImageIndex, setCurrentImageIndex] = useState(0);
	const [imageError, setImageError] = useState(false); // State to track if the image has failed to load


	const getImageSource = () => {
		// Try to get image from specifications first (usually higher quality)
		let image = null;

		if (product.specifications?.product_image_1) {
			image = product.specifications.product_image_1;
		} else if (product.images?.length) {
			image = product.images[0];
		}

		// If there's an error or no images available, use fallback
		if (imageError || !image) {
			return product.brand?.logoUrl ||
				`https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(product.name)}`;
		}

		// If it's already a full URL, return it as is
		if (image.startsWith('http')) {
			return image;
		}

		// Construct the Supabase storage URL
		return `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
	};

	// Calculate the latest offer
	const latestOffer = (product.retailerOffers || []).reduce((latest, offer) => {
		if (!latest || new Date(offer.createdAt) > new Date(latest.createdAt)) {
			return offer;
		}
		return latest;
	}, null as RetailerOffer | null);

	return (
		<Link href={`/products/${product.slug}`} className="block">
			<motion.div
				key={product.id}
				className="relative bg-white rounded-lg shadow-lg overflow-hidden"
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				whileHover={{ scale: 1.02 }}
				transition={{ duration: 0.2 }}
			>
				<div className="aspect-square bg-secondary/10 rounded-lg overflow-hidden relative">
					<div className="absolute top-2 right-2 bg-blue-300/80 text-white px-4 py-2 rounded-full text-sm font-medium z-10">
						£{(product.cashbackAmount || 0).toFixed(2)} Cashback
					</div>

{product.promotion?.purchaseEndDate && (
					<div className="absolute top-2 left-2 bg-accent text-white text-xs px-2 py-1 rounded">
						{getTimeLeft(product.promotion.purchaseEndDate)}
					</div>
				)}

					<Image
						src={getImageSource()}
						alt={product.name}
						fill
						sizes="(max-width: 768px) 100vw, 300px"
						className="object-contain p-4"
						onError={() => setImageError(true)}
						priority
					/>
				</div>
				{/* Adjusted height to remove extra space */}
				<div className="p-6 bg-white relative z-10 flex flex-col">
					<h3 className="text-sm font-semibold text-primary mb-2 line-clamp-2">
						{product.brand?.name || 'Unknown Brand'} - {product.name}
					</h3>
					<div className="mt-auto">
						{/* Temporarily commented out price display - keeping the code but not rendering
						<div className="space-y-2">
							<p className="text-lg font-bold text-primary">
								{product.minPrice !== null ? `£${Number(product.minPrice).toFixed(2)}` : 'Price not available'}
							</p>
						</div>
						*/}
						<p className="text-sm text-accent">
							Up to £{(product.cashbackAmount || 0).toFixed(2)} Cashback
						</p>
						{product.retailerOffers && product.retailerOffers.length > 0 && (
							<p className="text-sm text-foreground/70">
								{product.retailerOffers.length} retailer{product.retailerOffers.length !== 1 ? 's' : ''} available
							</p>
						)}
					</div>
				</div>
			</motion.div>
		</Link>
	);
}
