/**
 * Server-side brand data access layer
 * 
 * This module provides server-side functions for fetching and transforming
 * brand data from Supabase. Optimized for SEO and performance.
 */

import { createCacheableSupabaseClient } from '@/lib/supabase/server'
import { createCachedFunction, cacheKeys, CACHE_DURATIONS, CACHE_TAGS } from '@/lib/cache'
import { Promotion } from '@/types/database'
import { formatDate, formatDateRange, isPastDate, getTimeRemaining } from '@/app/utils/date'
import type {
  Brand,
  TransformedBrand,
  TransformedProduct,
  TransformedPromotion,
  PaginatedResponse,
  BrandResponse,
  BrandPageResponse,
} from './types'

/**
 * Transform raw brand data from database to API format
 */
function transformBrand(rawBrand: any): TransformedBrand {
  return {
    id: rawBrand.id,
    name: rawBrand.name,
    slug: rawBrand.slug,
    logoUrl: rawBrand.logo_url,
    description: rawBrand.description,
    featured: rawBrand.featured || false,
    sponsored: rawBrand.sponsored || false,
    createdAt: rawBrand.created_at,
    updatedAt: rawBrand.updated_at,
    productsCount: rawBrand.products_count || 0,
    activePromotions: rawBrand.active_promotions || [],
  }
}

/**
 * Transform promotion data for brand context
 */
function transformPromotion(rawPromotion: any): TransformedPromotion {
  // Determine if the promotion is active based on dates and status
  const now = new Date();
  const startDate = new Date(rawPromotion.purchase_start_date);
  const endDate = new Date(rawPromotion.purchase_end_date);
  const isActive = rawPromotion.status === 'active' && 
                  now >= startDate && 
                  now <= endDate;
  const isExpired = new Date(rawPromotion.purchase_end_date) < now;
  
  // Calculate time remaining if promotion is active
  const timeRemaining = isActive ? getTimeRemaining(rawPromotion.purchase_end_date) : null;
  
  // Format dates for display
  const formattedPurchaseDateRange = formatDateRange(
    rawPromotion.purchase_start_date,
    rawPromotion.purchase_end_date
  );
  
  const formattedExpiryDate = formatDate(rawPromotion.purchase_end_date, {
    includeTime: true,
    dateOptions: { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' },
  });

  return {
    id: rawPromotion.id,
    title: rawPromotion.title,
    description: rawPromotion.description,
    maxCashbackAmount: rawPromotion.max_cashback_amount,
    purchaseStartDate: rawPromotion.purchase_start_date,
    purchaseEndDate: rawPromotion.purchase_end_date,
    claimStartOffsetDays: rawPromotion.claim_start_offset_days || 0,
    claimWindowDays: rawPromotion.claim_window_days || 30, // Default 30 days claim window
    termsUrl: rawPromotion.terms_url,
    termsDescription: rawPromotion.terms_description,
    status: isActive ? 'active' : 'expired',
    isFeatured: Boolean(rawPromotion.is_featured),
    brand: rawPromotion.brand || null,
    category: rawPromotion.category || null,
    isActive,
    isExpired,
    timeRemaining,
    formattedPurchaseDateRange,
    formattedExpiryDate,
  };
}

/**
 * Get a single brand by ID with basic information
 */
async function _getBrand(id: string): Promise<TransformedBrand | null> {
  try {
    const supabase = createCacheableSupabaseClient()

    const { data, error } = await supabase
      .from('brands')
      .select(`
        *
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching brand:', error)
      return null
    }

    return transformBrand(data)
  } catch (error) {
    console.error('Exception in getBrand:', error)
    return null
  }
}

/**
 * Cached version of getBrand
 */
export const getBrand = createCachedFunction(
  _getBrand,
  {
    key: 'getBrand',
    revalidate: CACHE_DURATIONS.LONG,
    tags: [CACHE_TAGS.BRAND, CACHE_TAGS.BRANDS],
  }
)

/**
 * Get all brands with pagination
 */
async function _getBrands(page = 1, limit = 20): Promise<PaginatedResponse<TransformedBrand>> {
  try {
    const supabase = createCacheableSupabaseClient()
    const offset = (page - 1) * limit

    const { data, error, count } = await supabase
      .from('brands')
      .select(`
        *
      `, { count: 'exact' })
      .order('name', { ascending: true })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching brands:', error)
      throw new Error(`Failed to fetch brands: ${error.message}`)
    }

    const transformedBrands = (data || []).map(transformBrand)
    const total = count || 0
    const totalPages = Math.ceil(total / limit)

    return {
      data: transformedBrands,
      pagination: {
        page,
        limit,
        total,
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('Exception in getBrands:', error)
    throw error
  }
}

/**
 * Cached version of getBrands
 */
export const getBrands = createCachedFunction(
  _getBrands,
  {
    key: 'getBrands',
    revalidate: CACHE_DURATIONS.LONG,
    tags: [CACHE_TAGS.BRANDS],
  }
)

/**
 * Get featured brands
 */
async function _getFeaturedBrands(limit = 10): Promise<TransformedBrand[]> {
  try {
    const supabase = createCacheableSupabaseClient()

    const { data, error } = await supabase
      .from('brands')
      .select(`
        *
      `)
      .eq('featured', true)
      .order('name', { ascending: true })
      .limit(limit)

    if (error) {
      console.error('Error fetching featured brands:', error)
      return []
    }

    return (data || []).map(transformBrand)
  } catch (error) {
    console.error('Exception in getFeaturedBrands:', error)
    return []
  }
}

/**
 * Cached version of getFeaturedBrands
 */
export const getFeaturedBrands = createCachedFunction(
  _getFeaturedBrands,
  {
    key: 'getFeaturedBrands',
    revalidate: CACHE_DURATIONS.LONG,
    tags: [CACHE_TAGS.FEATURED, CACHE_TAGS.BRANDS],
  }
)

/**
 * Get brand with its products and promotions for brand pages
 */
async function _getBrandWithDetails(id: string): Promise<BrandResponse | null> {
  try {
    const supabase = createCacheableSupabaseClient()

    // Get brand basic info
    const brand = await _getBrand(id)
    if (!brand) {
      return null
    }

    // Get featured products for this brand
    const { data: productsData, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url
        ),
        category:category_id (
          id,
          name,
          slug
        ),
        promotion:promotion_id (
          id,
          title,
          max_cashback_amount
        ),
        product_retailer_offers (
          id,
          price,
          stock_status,
          url,
          retailer:retailer_id (
            id,
            name,
            logo_url
          )
        )
      `)
      .eq('brand_id', id)
      .eq('status', 'active')
      .order('is_featured', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(12)

    if (productsError) {
      console.error('Error fetching brand products:', productsError)
    }

    // Get active and recently expired promotions for this brand (last 20)
    const { data: promotionsData, error: promotionsError } = await supabase
      .from('promotions')
      .select(`
        *,
        brand:brand_id (
          id,
          name,
          slug,
          logo_url
        ),
        category:category_id (
          id,
          name,
          slug
        )
      `)
      .eq('brand_id', id)
      .in('status', ['active', 'expired']) // Include both active and expired
      .order('purchase_end_date', { ascending: false }) // Most recent first
      .order('is_featured', { ascending: false })
      .limit(20) // Get up to 20 promotions

    if (promotionsError) {
      console.error('Error fetching brand promotions:', promotionsError)
    }

    // Helper function to parse price from specifications
    const parseSpecificationPrice = (priceString: string | null | undefined): number | null => {
      if (!priceString || typeof priceString !== 'string') return null;
      const cleanPrice = priceString.replace(/[£$€,\s]/g, '');
      const parsed = parseFloat(cleanPrice);
      return isNaN(parsed) ? null : parsed;
    };

    // Transform products
    const featuredProducts = (productsData || []).map((product: any) => {
      // Calculate minPrice from retailer offers or specifications
      const retailerOffers = (product.product_retailer_offers || []);
      const retailerPrices = retailerOffers.map((offer: any) => offer.price).filter((price: any) => price != null);
      const specificationPrice = parseSpecificationPrice(product.specifications?.price);

      let minPrice: number | null = null;
      if (retailerPrices.length > 0) {
        minPrice = Math.min(...retailerPrices);
      } else if (specificationPrice !== null) {
        minPrice = specificationPrice;
      }

      return {
        id: product.id,
        name: product.name,
        slug: product.slug,
        description: product.description || '',
        images: product.images || [],
        specifications: product.specifications || null,
        status: product.status || 'active',
        isFeatured: product.is_featured || false,
        isSponsored: product.is_sponsored || false,
        cashbackAmount: product.cashback_amount,
        minPrice: minPrice,
        modelNumber: product.model_number || '',
        createdAt: product.created_at,
        updatedAt: product.updated_at,
        brand: product.brand,
        category: product.category,
        promotion: product.promotion,
        retailerOffers: (product.product_retailer_offers || []).map((offer: any) => ({
        id: offer.id,
        retailer: {
          id: offer.retailer?.id || '',
          name: offer.retailer?.name || '',
          logoUrl: offer.retailer?.logo_url || null,
          websiteUrl: offer.retailer?.website_url || null,
        },
        price: offer.price,
        stockStatus: offer.stock_status || 'unknown',
          url: offer.url || null,
          createdAt: offer.created_at,
        })),
      };
    });

    // Transform promotions
    const activePromotions = (promotionsData || []).map(transformPromotion)

    return {
      brand,
      featuredProducts: featuredProducts,
      activePromotions: activePromotions,
    }
  } catch (error) {
    console.error('Exception in getBrandWithDetails:', error)
    return null
  }
}

/**
 * Cached version of getBrandWithDetails
 */
export const getBrandWithDetails = createCachedFunction(
  _getBrandWithDetails,
  {
    key: 'getBrandWithDetails',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.BRAND, CACHE_TAGS.PRODUCTS, CACHE_TAGS.PROMOTIONS],
  }
)

/**
 * Get brand by slug (for SEO-friendly URLs)
 */
async function _getBrandBySlug(slug: string): Promise<TransformedBrand | null> {
  try {
    const supabase = createCacheableSupabaseClient()

    const { data, error } = await supabase
      .from('brands')
      .select(`
        *
      `)
      .eq('slug', slug)
      .single()

    if (error) {
      console.error('Error fetching brand by slug:', error)
      return null
    }

    return transformBrand(data)
  } catch (error) {
    console.error('Exception in getBrandBySlug:', error)
    return null
  }
}

/**
 * Cached version of getBrandBySlug
 */
export const getBrandBySlug = createCachedFunction(
  _getBrandBySlug,
  {
    key: 'getBrandBySlug',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.BRAND],
  }
)

/**
 * Get brand page data with promotions for SSG rendering
 * Supports both UUID and slug lookups
 */
// Helper function to check if a string is a valid UUID
function isUuid(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

async function _getBrandPromotions(brand: any): Promise<BrandPageResponse> {
  const now = new Date();
  const supabase = createCacheableSupabaseClient();
  let promotionsData: any[] = [];

  try {
    console.log(`Fetching promotions for brand ID: ${brand.id} (${brand.name || 'unknown'})`);
    
    // Get all promotions for this brand with related data
    const { data, error, status, statusText } = await supabase
      .from('promotions')
      .select(`
        *,
        category:categories(id, name, slug),
        brand:brands(id, name, slug, logo_url)
      `)
      .eq('brand_id', brand.id)
      .order('purchase_start_date', { ascending: false })
      .order('purchase_end_date', { ascending: true });

    console.log('Promotions query status:', { status, statusText });
    
    if (error) {
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
        brandId: brand.id
      });
      throw new Error(`Failed to fetch promotions: ${error.message}`);
    }
    
    promotionsData = data || [];
    console.log(`Found ${promotionsData.length} promotions for brand ${brand.id}`);

  } catch (error) {
    console.error('Unexpected error in _getBrandPromotions:', error);
    // Return empty promotions but still return the brand
    return {
      brand: transformBrand(brand),
      promotions: [],
      activePromotions: [],
      expiredPromotions: [],
      promotionCount: 0,
      activePromotionCount: 0,
      expiredPromotionCount: 0,
    };
  }

  // Transform the brand data
  const transformedBrand = transformBrand(brand);
  
  // Transform and filter promotions
  const allPromotions = promotionsData.map(transformPromotion);
  const activePromotions = allPromotions.filter(
    (p) => new Date(p.purchaseStartDate) <= now && new Date(p.purchaseEndDate) >= now
  );
  const expiredPromotions = allPromotions.filter((p) => new Date(p.purchaseEndDate) < now);

  return {
    brand: transformedBrand,
    promotions: allPromotions,
    activePromotions,
    expiredPromotions,
    promotionCount: allPromotions.length,
    activePromotionCount: activePromotions.length,
    expiredPromotionCount: expiredPromotions.length,
  };
}

async function _getBrandPageData(identifier: string): Promise<BrandPageResponse | null> {
  try {
    const supabase = createCacheableSupabaseClient();
    
    // 1. Try exact slug match first (case-sensitive)
    const { data: brandBySlug, error: slugError } = await supabase
      .from('brands')
      .select('*')
      .eq('slug', identifier)  // Exact match only
      .maybeSingle();

    if (slugError) {
      console.error('Error fetching brand by slug:', slugError);
      return null;
    }

    if (brandBySlug) {
      return await _getBrandPromotions(brandBySlug);
    }
    
    // 2. Try by ID if identifier is a UUID (for backward compatibility)
    if (isUuid(identifier)) {
      const { data: brandById, error: idError } = await supabase
        .from('brands')
        .select('*')
        .eq('id', identifier)
        .maybeSingle();

      if (idError) {
        console.error('Error fetching brand by ID:', idError);
        return null;
      }

      if (brandById) {
        return await _getBrandPromotions(brandById);
      }
    }
    
    console.error('Brand not found with identifier:', identifier);
    return null;
  } catch (error) {
    console.error('Error in _getBrandPageData:', error);
    return null;
  }
}

/**
 * Cached version of getBrandPageData
 */
export const getBrandPageData = createCachedFunction(
  _getBrandPageData,
  {
    key: 'getBrandPageData',
    revalidate: CACHE_DURATIONS.SHORT, // 5 minutes
    tags: [CACHE_TAGS.BRAND, CACHE_TAGS.PROMOTION],
  }
)
