/**
 * Data layer exports
 * 
 * This module provides a centralized export for all server-side data functions.
 * Import from this file to access any data layer functionality.
 */

// Product data functions
export {
  getProduct,
  getProducts,
  getFeaturedProducts,
  getSimilarProducts,
  getProductWithSimilar,
  getProductBySlug,
} from './products'

// Brand data functions
export {
  getBrand,
  getBrands,
  getFeaturedBrands,
  getBrandWithDetails,
  getBrandBySlug,
} from './brands'

// Promotion data functions
export {
  getPromotion,
  getFeaturedPromotions,
  getActivePromotions,
  getPromotionsByBrand,
  getPromotionsByCategory,
  getPromotions,
} from './promotions'

// Search data functions
export {
  searchProducts,
  getSearchSuggestions,
  getPopularSearchTerms,
} from './search'

// Retailer data functions
export {
  getRetailer,
  getRetailers,
  getFeaturedRetailers,
  getRetailerWithProducts,
  getRetailerBySlug,
} from './retailers'

// Type exports
export type {
  Product,
  Brand,
  Category,
  Retailer,
  Promotion,
  ProductRetailerOffer,
  TransformedProduct,
  TransformedBrand,
  TransformedRetailer,
  TransformedPromotion,
  TransformedRetailerOffer,
  ProductFilters,
  RetailerFilters,
  PaginationParams,
  PaginatedResponse,
  ApiResponse,
  ProductResponse,
  BrandResponse,
  RetailerResponse,
  SearchFilters,
  SearchResult,
  DataError,
  CacheConfig,
} from './types'
