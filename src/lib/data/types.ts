/**
 * Shared TypeScript interfaces for data layer
 * 
 * This module defines the core data types used throughout the application
 * for products, brands, promotions, and related entities.
 */

// Base entity interface
export interface BaseEntity {
  id: string
  created_at: string
  updated_at: string
  version?: number
}

// Product-related interfaces
export interface Product extends BaseEntity {
  name: string
  slug: string
  brand_id: string | null
  category_id: string | null
  description: string | null
  specifications: Record<string, any> | null
  images: string[] | null
  status: string | null
  is_featured: boolean | null
  is_sponsored: boolean | null
  cashback_amount: number | null
  promotion_id: string | null
  model_number: string
  search_vector?: any
}

export interface Brand extends BaseEntity {
  name: string
  slug: string
  logo_url: string | null
  description: string | null
  featured: boolean | null
  sponsored: boolean | null
  search_vector?: any
}

export interface Category extends BaseEntity {
  name: string
  slug: string
  parent_id: string | null
  featured: boolean | null
  sponsored: boolean | null
}

export interface Retailer extends BaseEntity {
  name: string
  slug: string
  logo_url: string | null
  status: string | null
  featured: boolean | null
  sponsored: boolean | null
  website_url: string | null
  claim_period: string | null
}

export interface ClaimPeriod {
  startDate: string;  // ISO format
  endDate: string;    // ISO format
  formatted: string;  // Human-readable
}

export interface Promotion extends BaseEntity {
  brand_id: string | null
  category_id: string | null
  title: string
  description: string | null
  max_cashback_amount: number
  purchase_start_date: string
  purchase_end_date: string
  claim_start_offset_days: number
  claim_window_days: number
  terms_url: string | null
  terms_description: string | null
  status: string | null
  last_validated_at: string | null
  is_featured: boolean
  claimPeriod?: ClaimPeriod | null
}

export interface ProductRetailerOffer extends BaseEntity {
  product_id: string
  retailer_id: string
  price: number
  stock_status: string | null
  url: string | null
}

// Transformed interfaces for API responses
export interface TransformedProduct {
  id: string
  name: string
  slug: string
  description: string
  images: string[]
  specifications?: Record<string, any> | null
  status: string
  isFeatured: boolean
  isSponsored: boolean
  cashbackAmount: number | null
  minPrice: number | null
  modelNumber: string
  createdAt: string
  updatedAt: string
  brand: {
    id: string
    name: string
    slug: string
    logoUrl: string | null
    description?: string | null
  } | null
  category: Category | null
  promotion: ({
    id: string
    title: string
    description?: string | null
    maxCashbackAmount: number
    purchaseStartDate: string
    purchaseEndDate: string
    status: string
    claimStartOffsetDays: number
    claimWindowDays: number
    termsUrl?: string | null
    termsDescription?: string | null
    isFeatured?: boolean
    brand?: Brand | null
    category?: Category | null
    isActive?: boolean
    isExpired?: boolean
    claimPeriod?: ClaimPeriod | null
  }) | null
  retailerOffers: TransformedRetailerOffer[]
}

export interface TransformedRetailerOffer {
  id: string
  retailer: {
    id: string
    name: string
    logoUrl: string | null
    websiteUrl: string | null
  }
  price: number
  stockStatus: string | null
  url: string | null
  createdAt: string
}

export interface TransformedRetailer {
  id: string
  name: string
  slug: string
  logoUrl: string | null
  websiteUrl: string | null
  status: string
  featured: boolean
  sponsored: boolean
  claimPeriod: string | null
  createdAt: string
  updatedAt: string
}

export interface TransformedBrand {
  id: string
  name: string
  slug: string
  logoUrl: string | null
  description: string | null
  featured: boolean
  sponsored: boolean
  createdAt: string
  updatedAt: string
  productsCount?: number
  activePromotions?: TransformedPromotion[]
}

export interface TransformedPromotion {
  id: string
  title: string
  description: string | null
  maxCashbackAmount: number
  purchaseStartDate: string
  purchaseEndDate: string
  claimStartOffsetDays: number
  claimWindowDays: number
  termsUrl: string | null
  termsDescription: string | null
  status: string
  isFeatured: boolean
  brand: Brand | null
  category: Category | null
  isActive: boolean
  isExpired: boolean
  claimPeriod?: ClaimPeriod | null
  timeRemaining?: {
    days: number
    hours: number
    minutes: number
    seconds: number
  } | null
  formattedPurchaseDateRange?: string
  formattedExpiryDate?: string
}

// Filter option interfaces
export interface FilterPromotion extends Omit<Promotion, 'brand_id'> {
  brand_id: string | null;
  brand_name: string;
}

// Filter and pagination interfaces
// In src/lib/data/types.ts
export interface ProductFilters {
  // Existing filter fields
  brandId?: string;
  categoryId?: string;
  promotionId?: string;
  isFeatured?: boolean;
  isSponsored?: boolean;
  status?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  
  // Add pagination fields
  page?: number;
  pageSize?: number;
}

//? can this be removed as we are combining this within the product filters above?
export interface PaginationParams {
  page?: number
  limit?: number
  offset?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// API Response interfaces
export interface ApiResponse<T> {
  data: T | null
  error: string | null
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// new interface for the paginated products response
export interface PaginatedProductsResponse {
  product: TransformedProduct[];  // Changed from 'data' to 'product'
  similarProducts: TransformedProduct[];  // Added this line
  pagination: {
    page: number;
    limit: number;        // Changed from pageSize to limit
pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
 
  };
}


export interface ProductResponse {
  product: TransformedProduct
  similarProducts: TransformedProduct[]
}

// Brand page data response
export interface BrandPageResponse {
  brand: TransformedBrand;
  promotions: TransformedPromotion[];
  activePromotions: TransformedPromotion[];
  expiredPromotions: TransformedPromotion[];
  promotionCount: number;
  activePromotionCount: number;
  expiredPromotionCount: number;
  featuredProducts?: TransformedProduct[];
}



export interface BrandResponse {
  brand: TransformedBrand
  featuredProducts: TransformedProduct[]
  activePromotions: TransformedPromotion[]
}

export interface RetailerResponse {
  retailer: TransformedRetailer
  featuredProducts: TransformedProduct[]
  activeOffersCount: number
  productsCount: number
}

// Retailer filter interface
export interface RetailerFilters {
  featured?: boolean
  sponsored?: boolean
  status?: string
  search?: string
}

// Search interfaces
export interface SearchFilters {
  query?: string
  category?: string
  brand?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'newest' | 'featured'
}

export interface SearchResult {
  products: TransformedProduct[]
  total: number
  filtersApplied: SearchFilters
  suggestions?: string[]
}

// Error interfaces
export interface DataError {
  message: string
  code?: string
  details?: any
}

// Cache interfaces
export interface CacheConfig {
  key: string
  revalidate?: number
  tags?: string[]
}
