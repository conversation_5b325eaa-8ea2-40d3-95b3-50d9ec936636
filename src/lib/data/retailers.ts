/**
 * Server-side retailer data access layer
 * 
 * This module provides server-side functions for fetching and transforming
 * retailer data from Supabase. All functions are optimized for SEO and performance.
 */

import { createCacheableSupabaseClient } from '@/lib/supabase/server'
import { createCachedFunction, cacheKeys, CACHE_DURATIONS, CACHE_TAGS } from '@/lib/cache'
import type {
  Retailer,
  TransformedRetailer,
  TransformedProduct,
  RetailerFilters,
  PaginatedResponse,
  RetailerResponse,
  DataError,
} from './types'

/**
 * Transform raw retailer data from database to API format
 */
function transformRetailer(retailer: any): TransformedRetailer {
  return {
    id: retailer.id,
    name: retailer.name,
    slug: retailer.slug,
    logoUrl: retailer.logo_url,
    websiteUrl: retailer.website_url,
    status: retailer.status || 'active',
    featured: retailer.featured || false,
    sponsored: retailer.sponsored || false,
    claimPeriod: retailer.claim_period,
    createdAt: retailer.created_at,
    updatedAt: retailer.updated_at || retailer.created_at,
  }
}

/**
 * Transform product data for retailer responses
 */
function transformProductForRetailer(product: any): TransformedProduct {
  // Calculate minPrice from retailer offers if available, otherwise use specification price or null
  const retailerPrices = (product.retailer_offers || [])
    .map((offer: any) => offer.price)
    .filter((price: any) => price != null);
  
  const specificationPrice = product.specifications?.price ? 
    parseFloat(product.specifications.price.replace(/[^0-9.]/g, '')) : null;
  
  let minPrice: number | null = null;
  if (retailerPrices.length > 0) {
    minPrice = Math.min(...retailerPrices);
  } else if (specificationPrice !== null) {
    minPrice = specificationPrice;
  }

  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    description: product.description || '',
    images: product.images || [],
    specifications: product.specifications || null,
    status: product.status || 'active',
    isFeatured: product.is_featured || false,
    isSponsored: product.is_sponsored || false,
    cashbackAmount: product.cashback_amount,
    minPrice,
    modelNumber: product.model_number || '',
    createdAt: product.created_at,
    updatedAt: product.updated_at || product.created_at,
    brand: product.brand,
    category: product.category,
    promotion: product.promotion,
    retailerOffers: (product.retailer_offers || []).map((offer: any) => ({
      id: offer.id,
      retailer: {
        id: offer.retailer_id,
        name: offer.retailer_name || '',
        logoUrl: offer.retailer_logo_url || null,
        websiteUrl: offer.retailer_website_url || null,
      },
      price: offer.price,
      stockStatus: offer.stock_status || 'unknown',
      url: offer.url || null,
      createdAt: offer.created_at,
    }))
  }
}

/**
 * Get a single retailer by ID with all related data
 * Optimized for SEO with server-side rendering
 */
async function _getRetailer(id: string): Promise<TransformedRetailer | null> {
  try {
    const supabase = createCacheableSupabaseClient()

    const { data, error } = await supabase
      .from('retailers')
      .select('*')
      .eq('id', id)
      .eq('status', 'active')
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching retailer:', error)
      throw new Error(`Failed to fetch retailer: ${error.message}`)
    }

    return transformRetailer(data)
  } catch (error) {
    console.error('Exception in getRetailer:', error)
    throw error
  }
}

/**
 * Cached version of getRetailer
 */
export const getRetailer = createCachedFunction(
  _getRetailer,
  {
    key: 'getRetailer',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.RETAILERS],
  }
)

/**
 * Get a single retailer by slug
 */
async function _getRetailerBySlug(slug: string): Promise<TransformedRetailer | null> {
  try {
    const supabase = createCacheableSupabaseClient()

    const { data, error } = await supabase
      .from('retailers')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'active')
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching retailer by slug:', error)
      throw new Error(`Failed to fetch retailer: ${error.message}`)
    }

    return transformRetailer(data)
  } catch (error) {
    console.error('Exception in getRetailerBySlug:', error)
    throw error
  }
}

/**
 * Cached version of getRetailerBySlug
 */
export const getRetailerBySlug = createCachedFunction(
  _getRetailerBySlug,
  {
    key: 'getRetailerBySlug',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.RETAILERS],
  }
)

/**
 * Get retailers with pagination and filtering
 */
async function _getRetailers(
  filters: RetailerFilters = {},
  page = 1,
  limit = 20
): Promise<PaginatedResponse<TransformedRetailer>> {
  try {
    const supabase = createCacheableSupabaseClient()
    const offset = (page - 1) * limit

    let query = supabase
      .from('retailers')
      .select('*', { count: 'exact' })
      .eq('status', 'active')
      .order('featured', { ascending: false })
      .order('sponsored', { ascending: false })
      .order('name', { ascending: true })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured)
    }

    if (filters.sponsored !== undefined) {
      query = query.eq('sponsored', filters.sponsored)
    }

    if (filters.status) {
      query = query.eq('status', filters.status)
    }

    if (filters.search) {
      query = query.ilike('name', `%${filters.search}%`)
    }

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching retailers:', error)
      throw new Error(`Failed to fetch retailers: ${error.message}`)
    }

    const transformedRetailers = (data || []).map(transformRetailer)
    const total = count || 0
    const totalPages = Math.ceil(total / limit)

    return {
      data: transformedRetailers,
      pagination: {
        page,
        limit,
        total,
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  } catch (error) {
    console.error('Exception in getRetailers:', error)
    throw error
  }
}

/**
 * Cached version of getRetailers
 */
export const getRetailers = createCachedFunction(
  _getRetailers,
  {
    key: 'getRetailers',
    revalidate: CACHE_DURATIONS.SHORT,
    tags: [CACHE_TAGS.RETAILERS],
  }
)

/**
 * Get featured retailers
 */
async function _getFeaturedRetailers(limit = 10): Promise<TransformedRetailer[]> {
  try {
    const supabase = createCacheableSupabaseClient()

    const { data, error } = await supabase
      .from('retailers')
      .select('*')
      .eq('status', 'active')
      .eq('featured', true)
      .order('sponsored', { ascending: false })
      .order('name', { ascending: true })
      .limit(limit)

    if (error) {
      console.error('Error fetching featured retailers:', error)
      throw new Error(`Failed to fetch featured retailers: ${error.message}`)
    }

    return (data || []).map(transformRetailer)
  } catch (error) {
    console.error('Exception in getFeaturedRetailers:', error)
    throw error
  }
}

/**
 * Cached version of getFeaturedRetailers
 */
export const getFeaturedRetailers = createCachedFunction(
  _getFeaturedRetailers,
  {
    key: 'getFeaturedRetailers',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.RETAILERS],
  }
)

/**
 * Get retailer with featured products and offer count
 */
async function _getRetailerWithProducts(id: string): Promise<RetailerResponse | null> {
  try {
    const supabase = createCacheableSupabaseClient()

    // Get retailer details
    const retailer = await _getRetailer(id)
    if (!retailer) {
      return null
    }

    // First get product IDs that have offers from this retailer
    const { data: offerData, error: offerError } = await supabase
      .from('product_retailer_offers')
      .select('product_id')
      .eq('retailer_id', id)

    if (offerError) {
      console.error('Error fetching retailer offers:', offerError)
      throw new Error(`Failed to fetch retailer offers: ${offerError.message}`)
    }

    const productIds = (offerData || []).map((offer: { product_id: string }) => offer.product_id)

    // Get featured products from this retailer
    let productsData = []
    let productsError = null

    if (productIds.length > 0) {
      const result = await supabase
        .from('products')
        .select(`
          *,
          brand:brand_id (
            id,
            name,
            slug,
            logo_url
          ),
          category:category_id (
            id,
            name,
            slug
          ),
          promotion:promotion_id (
            id,
            title,
            description,
            max_cashback_amount,
            status
          )
        `)
        .eq('status', 'active')
        .eq('is_featured', true)
        .in('id', productIds)
        .limit(8)

      productsData = result.data || [];
      productsError = result.error;
    }

    if (productsError) {
      console.error('Error fetching retailer products:', productsError)
      throw new Error(`Failed to fetch retailer products: ${productsError.message}`)
    }

    // Get total active offers count for this retailer
    const { count: offersCount, error: countError } = await supabase
      .from('product_retailer_offers')
      .select('*', { count: 'exact', head: true })
      .eq('retailer_id', id)

    if (countError) {
      console.error('Error counting retailer offers:', countError)
      throw new Error(`Failed to count retailer offers: ${countError.message}`)
    }

    const featuredProducts = (productsData || []).map(transformProductForRetailer)

    return {
      retailer,
      featuredProducts: featuredProducts,
      activeOffersCount: offersCount || 0,
      productsCount: featuredProducts.length,
    }
  } catch (error) {
    console.error('Exception in getRetailerWithProducts:', error)
    throw error
  }
}

/**
 * Cached version of getRetailerWithProducts
 */
export const getRetailerWithProducts = createCachedFunction(
  _getRetailerWithProducts,
  {
    key: 'getRetailerWithProducts',
    revalidate: CACHE_DURATIONS.MEDIUM,
    tags: [CACHE_TAGS.RETAILERS, CACHE_TAGS.PRODUCTS],
  }
)
