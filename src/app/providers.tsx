'use client';
export const runtime = 'edge';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState, useEffect } from 'react'
import { cancelAllRequests, resetRateLimits } from '@/lib/requestUtils'
import { PaginationProvider } from '@/contexts/PaginationContext'

export function Providers({ children }: { children: React.ReactNode }) {
    const [queryClient] = useState(() => new QueryClient({
        defaultOptions: {
            queries: {
                staleTime: 30 * 60 * 1000, // Increase to 30 minutes (from 5 minutes)
                gcTime: 60 * 60 * 1000,    // Increase to 60 minutes (from 10 minutes)
                refetchOnWindowFocus: false,
                refetchOnMount: false,
                refetchOnReconnect: false,
                retry: (failureCount, error) => {
                    // Don't retry on 4xx errors
                    if (error instanceof Error && 'status' in error && (error as any).status >= 400 && (error as any).status < 500) {
                        return false;
                    }
                    // Only retry 3 times
                    return failureCount < 3;
                },
                retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff with max 30s
            },
            mutations: {
                retry: 1,
                retryDelay: 1000,
            },
        },
    }))

    // Clean up all pending requests when the app unmounts
    useEffect(() => {
        return () => {
            cancelAllRequests();
            resetRateLimits();
        };
    }, []);

    return (
        <QueryClientProvider client={queryClient}>
            <PaginationProvider>
                {children}
                <ReactQueryDevtools initialIsOpen={false} />
            </PaginationProvider>
        </QueryClientProvider>
    )
}
