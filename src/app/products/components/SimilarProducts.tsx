import React from 'react'
import { ProductCard } from '@/components/ProductCard'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { TransformedProduct } from '@/lib/data/types'

interface SimilarProductsProps {
    products: TransformedProduct[]
}

export function SimilarProducts({ products }: SimilarProductsProps) {
    const [currentIndex, setCurrentIndex] = React.useState(0);
    const productsPerPage = 3;

    const canScrollLeft = currentIndex > 0;
    const canScrollRight = currentIndex + productsPerPage < products.length;

    const scrollLeft = () => {
        if (canScrollLeft) {
            setCurrentIndex(prev => prev - 1);
        }
    };

    const scrollRight = () => {
        if (canScrollRight) {
            setCurrentIndex(prev => prev + 1);
        }
    };

    // Calculate min price for each product, defaulting to null if no offers
    const productsWithMinPrice = products.map(product => {
        const prices = (product.retailerOffers || []).map(offer => offer.price);
        const minPrice = prices.length > 0 ? Math.min(...prices) : null;
        
        return {
            ...product,
            minPrice,
            slug: product.slug,
            // Ensure all required fields are included
            cashbackAmount: product.cashbackAmount || 0,
            promotion: product.promotion || null,
            category: product.category || null,
            brand: product.brand || null,
            retailerOffers: product.retailerOffers || []
        };
    });

    return (
        <div className="mt-16">
            <h2 className="text-2xl font-bold mb-8">Similar Products</h2>
            <div className="relative">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {productsWithMinPrice
                        .slice(currentIndex, currentIndex + productsPerPage)
                        .map(product => (
                            <ProductCard key={product.id} product={product} />
                        ))}
                </div>
                {products.length > productsPerPage && (
                    <>
                        <button
                            onClick={scrollLeft}
                            disabled={!canScrollLeft}
                            className={`absolute -left-4 top-1/2 -translate-y-1/2 bg-white p-2 rounded-full shadow-md ${!canScrollLeft ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                                }`}
                        >
                            <ChevronLeft className="h-6 w-6" />
                        </button>
                        <button
                            onClick={scrollRight}
                            disabled={!canScrollRight}
                            className={`absolute -right-4 top-1/2 -translate-y-1/2 bg-white p-2 rounded-full shadow-md ${!canScrollRight ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                                }`}
                        >
                            <ChevronRight className="h-6 w-6" />
                        </button>
                    </>
                )}
            </div>
        </div>
    )
} 