'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { useQuery } from '@tanstack/react-query'
import { SlidersHorizontal } from 'lucide-react'
import { useSearchParams } from 'next/navigation'
import { FilterMenu } from '@/components/FilterMenu'
import { ProductCard } from '@/components/ProductCard'
import { Pagination, PaginationInfo } from '@/components/ui/pagination'
import { TransformedProduct } from '@/lib/data/types'

// Types
interface ProductCardProps {
    product: TransformedProduct;
}

interface ApiResponse {
    data: TransformedProduct[] | null;
    error: string | null;
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

// Fetch Products Function (Client-side only)
async function fetchProducts(
    page: number = 1,
    filters?: {
        brandId?: string;
        promotionId?: string;
        priceRange: [number, number];
    }
): Promise<ApiResponse> {
    const params = new URLSearchParams();
    params.append('page', page.toString());

    if (filters) {
        if (filters.brandId) params.append('brandId', filters.brandId);
        if (filters.promotionId) params.append('promotion_id', filters.promotionId);
        if (filters.priceRange[0] > 0) params.append('minPrice', filters.priceRange[0].toString());
        if (filters.priceRange[1] < 99999) params.append('maxPrice', filters.priceRange[1].toString());
    }

    try {
        const response = await fetch(`/api/products?${params.toString()}`);
        
        if (!response.ok) {
            throw new Error(`Network response was not ok: ${response.statusText}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Error fetching products:', error);
        return {
            data: null,
            error: error instanceof Error ? error.message : 'Failed to fetch products',
            pagination: undefined
        };
    }
}

interface ProductsContentProps {
    initialData?: ApiResponse;
    filterOptions: {
        brands: Array<{ id: string; name: string }>;
        promotions: Array<{ 
            id: string; 
            title: string;
            brand_id: string;
            brand_name: string;
        }>;
    };
}

export default function ProductsContent({ initialData, filterOptions }: ProductsContentProps) {
    const searchParams = useSearchParams();
    const promotionId = searchParams?.get('promotion_id');
    // Temporarily disable filter menu to fix brand fetching error
    // To re-enable, uncomment the following lines and the filter menu JSX below
    // const [isFilterOpen, setIsFilterOpen] = useState(false);
    // const [currentPage, setCurrentPage] = useState(1);
    // State for filters - disabled
    // const [priceRange, setPriceRange] = useState<[number, number]>([0, 99999]);
    // const [appliedPriceRange, setAppliedPriceRange] = useState<[number, number]>([0, 99999]);
    // const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
    // const [selectedPromotions, setSelectedPromotions] = useState<string[]>(
    //     promotionId ? [promotionId] : []
    // );
    // const [isApplyingFilters, setIsApplyingFilters] = useState(false);
    
    // Simplified state for now
    const [currentPage, setCurrentPage] = useState(1);
    const appliedPriceRange: [number, number] = [0, 99999]; // Default range with no filtering

    const { data, isLoading, error } = useQuery<ApiResponse>({
        queryKey: ['products', currentPage, promotionId],
        queryFn: () => {
            console.log('Fetching products:', {
                page: currentPage,
                promotionId,
            });
            return fetchProducts(currentPage, {
                promotionId: promotionId || undefined,
                priceRange: appliedPriceRange
            });
        },
        // Only use initialData for the first page and when there's no promotion filter
        // This ensures we don't show all products briefly before filtering
        initialData: currentPage === 1 && initialData && !promotionId ? {
            data: initialData.data,
            error: initialData.error,
            pagination: initialData.pagination
        } : undefined,
        // Enable background refetching to ensure fresh data
        refetchOnMount: true,
        // Keep data fresh for 1 minute
        staleTime: 60 * 1000,
        // Don't refetch on window focus to prevent layout shifts
        refetchOnWindowFocus: false,
    });

    // Use server data for first render, then client data
    const products = data?.data || [];
    const pagination = data?.pagination || initialData?.pagination;
    const hasError = error || data?.error;

    // Filter-related handlers are temporarily disabled
    // To re-enable the filter menu, uncomment these handlers and the related state above
    /*
    const handleBrandToggle = (brandId: string) => {
        setSelectedBrands(prev => 
            prev.includes(brandId) 
                ? prev.filter(id => id !== brandId)
                : [brandId] // Single selection mode
        );
        setSelectedPromotions([]); // Clear promotions when brand is selected
        setCurrentPage(1); // Reset to first page when filters change
    };

    const handlePromotionToggle = (promotionId: string) => {
        setSelectedPromotions(prev => 
            prev.includes(promotionId)
                ? []
                : [promotionId] // Single selection mode
        );
        setSelectedBrands([]); // Clear brands when promotion is selected
        setCurrentPage(1); // Reset to first page when filters change
    };

    const handlePriceRangeChange = (range: [number, number]) => {
        setPriceRange(range);
        // Don't apply immediately, wait for Apply button
    };

    const handleApplyPriceRange = () => {
        setAppliedPriceRange([...priceRange]);
        setCurrentPage(1); // Reset to first page when filters change
        setIsApplyingFilters(false);
    };

    const handleOpenFilterMenu = () => {
        setIsFilterOpen(true);
        // Reset the price range to the currently applied range when opening the menu
        setPriceRange([...appliedPriceRange]);
    };

    const handleClearFilters = () => {
        setSelectedBrands([]);
        setSelectedPromotions([]);
        const defaultRange: [number, number] = [0, 99999];
        setPriceRange(defaultRange);
        setAppliedPriceRange(defaultRange);
        setCurrentPage(1);
        setIsFilterOpen(false);
    };
    
    // Log filter options for debugging
    React.useEffect(() => {
        console.log('Filter options:', filterOptions);
        console.log('Selected brands:', selectedBrands);
        console.log('Selected promotions:', selectedPromotions);
        console.log('Price range:', priceRange);
        console.log('Applied price range:', appliedPriceRange);
    }, [filterOptions, selectedBrands, selectedPromotions, priceRange, appliedPriceRange]);

    // Reset to page 1 when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [selectedBrands, selectedPromotions, priceRange]);
    */

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="container px-4 py-6 md:py-12">
                {/* Filter menu temporarily disabled - to re-enable, uncomment the following section and related state/handlers */}
                {/*
                <div className="md:hidden mb-4">
                    <button
                        onClick={handleOpenFilterMenu}
                        className="w-full flex items-center justify-center gap-2 p-3 bg-white rounded-lg border border-primary/10 text-sm font-medium"
                    >
                        <SlidersHorizontal className="h-4 w-4" />
                        {isFilterOpen ? 'Hide Filters' : 'Show Filters'}
                        {(selectedBrands.length > 0 || selectedPromotions.length > 0 || appliedPriceRange[0] > 0 || appliedPriceRange[1] < 99999) && (
                            <span className="ml-1 w-2 h-2 rounded-full bg-blue-500"></span>
                        )}
                    </button>
                </div>
                */}

                <div className="flex flex-col md:flex-row gap-6">
                    {/* Filter menu temporarily disabled - to re-enable, uncomment this aside and related state/handlers */}
                    {/*
                    <motion.aside
                        initial={false}
                        animate={{ opacity: 1 }}
                        className={`md:w-64 flex-shrink-0 ${isFilterOpen ? 'block' : 'hidden'} md:block bg-white border rounded-lg md:border-gray-200`}
                    >
                        <FilterMenu
                            priceRange={priceRange}
                            onPriceRangeChange={handlePriceRangeChange}
                            onApplyPriceRange={handleApplyPriceRange}
                            selectedBrands={selectedBrands}
                            onBrandToggle={handleBrandToggle}
                            selectedPromotions={selectedPromotions}
                            onPromotionToggle={handlePromotionToggle}
                            onClearFilters={handleClearFilters}
                            brands={filterOptions.brands}
                            promotions={filterOptions.promotions}
                            onCloseAction={() => setIsFilterOpen(false)}
                        />
                    </motion.aside>
                    */}

                    <div className="flex-1">
                        {isLoading ? (
                            <div className="text-center py-8">Loading...</div>
                        ) : hasError ? (
                            <div className="text-center py-8 text-red-500" data-testid="pagination-error">
                                Error loading products: {error?.message || data?.error || 'Unknown error occurred'}
                            </div>
                        ) : products.length > 0 ? (
                            <>
                                {/* Pagination Info */}
                                {pagination && (
                                    <div className="mb-6 flex justify-between items-center">
                                        <PaginationInfo
                                            currentPage={pagination.page}
                                            totalPages={pagination.totalPages}
                                            totalItems={pagination.total}
                                            itemsPerPage={pagination.limit}
                                        />
                                    </div>
                                )}

                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" data-testid="product-list">
                                    {products.map((product) => (
                                        <div key={product.id} data-testid="product-item">
                                            <ProductCard product={product} />
                                        </div>
                                    ))}
                                </div>

                                {/* Pagination Controls */}
                                {pagination && pagination.totalPages > 1 && (
                                    <div className="mt-8 flex justify-center">
                                        <Pagination
                                            currentPage={currentPage}
                                            totalPages={pagination.totalPages}
                                            onPageChange={setCurrentPage}
                                        />
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-8">
                                <h3 className="text-lg font-semibold mb-2">No products found</h3>
                                <p className="text-sm text-foreground/70">
                                    Try adjusting your filters or check back later
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
