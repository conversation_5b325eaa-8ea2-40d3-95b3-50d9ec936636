/**
 * Products Listing Page (Server Component)
 * 
 * This page uses Server-Side Rendering (SSR) for the initial load to improve SEO.
 * The initial data is fetched server-side and passed to the client component.
 * Subsequent client-side interactions (pagination, filtering) are handled by React Query.
 */

import { Suspense } from 'react';
import ProductsContent from './components/ProductsContent';
import { getProducts, getFilterOptions } from '@/lib/data/products';
import type { ProductFilters, PaginatedResponse, TransformedProduct, Brand, Promotion } from '@/lib/data/types';

// Define the shape of the promotion data we expect in the filter options
interface FilterPromotion {
    id: string;
    title: string;
    brand_id: string;
    brand_name: string;
}

// Type for the initial data passed to the client component
interface InitialProductsData {
    data: TransformedProduct[] | null;
    error: string | null;
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

// Type for filter options that matches the expected type in ProductsContent
interface FilterOptions {
    brands: Array<{ id: string; name: string }>;
    promotions: FilterPromotion[];
}

// Force dynamic rendering to ensure fresh data on each request
// This is important for an e-commerce site where product data might change frequently
export const dynamic = 'force-dynamic';

interface ProductsPageProps {
    searchParams: Promise<{
        page?: string;
        promotion_id?: string;
        brand_id?: string;
        category_id?: string;
        search?: string;
    }>;
}

/**
 * Main Products Page Component
 *
 * This is an async server component that:
 * 1. Fetches products server-side based on URL search parameters
 * 2. Fetches all available filter options server-side
 * 3. Renders the ProductsContent client component with initial data and filter options
 * 4. Provides a loading fallback during initial render
 * 5. Maintains pagination state via URL parameters for better UX
 */
export default async function ProductsPage({ searchParams }: ProductsPageProps) {
    const params = await searchParams;

    // Parse URL parameters
    const page = parseInt(params.page || '1', 10);
    const promotionId = params.promotion_id;
    const brandId = params.brand_id;
    const categoryId = params.category_id;
    const search = params.search;

    // Build filters object from URL parameters
    const filters: ProductFilters = {};
    if (promotionId) filters.promotion_id = promotionId;
    if (brandId) filters.brand_id = brandId;
    if (categoryId) filters.category_id = categoryId;
    if (search) filters.search = search;

    // Fetch initial data server-side based on URL parameters
    // This data will be used for the initial render and SEO
    const productsResponse = await getProducts({
        ...filters,
        // Pagination parameters
        page,           // Current page from URL
        pageSize: 20,   // 20 items per page
    });

    // Temporarily disable filter options to fix brand fetching error
    // TODO: Re-enable filter options once the issue is resolved
    const filterOptions: FilterOptions = {
        brands: [],  // Empty array since we're disabling brand filtering
        promotions: []  // Empty array since we're disabling promotion filtering
    };
    
    // Original code (commented out for reference)
    /*
    try {
        const filterOptionsRaw = await getFilterOptions();
        
        filterOptions = {
            brands: (filterOptionsRaw.brands || []).map(brand => ({
                id: brand.id,
                name: brand.name
            })),
            promotions: (filterOptionsRaw.promotions || []).map(promo => ({
                id: promo.id,
                title: promo.title,
                brand_id: promo.brand_id || '',
                brand_name: 'brand_name' in promo ? String(promo.brand_name) : 'Unknown Brand'
            }))
        };
    } catch (error) {
        console.error('Error loading filter options, continuing with empty filters:', error);
        // Continue with empty filters
    }
    */

    // Prepare the initial data for the client component
    const initialData: InitialProductsData = {
        data: productsResponse?.product || null,
        error: null, // getProducts throws on error, so we only get here on success
        pagination: productsResponse?.pagination
    };

    return (
        <div className="container mx-auto px-4 py-8">
            <Suspense
                fallback={
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                }
            >
                <ProductsContent
                    initialData={initialData}
                    filterOptions={filterOptions}
                    initialPage={page}
                    initialFilters={{
                        promotionId,
                        brandId,
                        categoryId,
                        search
                    }}
                />
            </Suspense>
        </div>
    );
}
