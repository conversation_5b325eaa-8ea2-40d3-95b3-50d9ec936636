'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { BrandWithDetails } from '@/types/brand'

/**
 * Client-side container for the brands listing page
 * 
 * @component
 * @param {Object} props - Component props
 * @param {BrandWithDetails[]} props.initialBrands - Array of brand data in camelCase format
 * 
 * @note This component expects brand data in camelCase format (e.g., logoUrl, createdAt, updatedAt)
 * to match the Brand interface from '@/types/database'.
 */

type BrandsClientProps = {
  initialBrands: BrandWithDetails[]
}

export function BrandsClient({ initialBrands }: BrandsClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeLetter, setActiveLetter] = useState<string>('A')
  
  // Filter brands based on search query
  const filteredBrands = initialBrands.filter(brand =>
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Group brands by first letter
  const groupedBrands = filteredBrands.reduce((acc, brand) => {
    const firstLetter = brand.name.charAt(0).toUpperCase()
    const letter = /[A-Z]/.test(firstLetter) ? firstLetter : '#'
    if (!acc[letter]) acc[letter] = []
    acc[letter].push(brand)
    return acc
  }, {} as Record<string, BrandWithDetails[]>)

  // Sort the grouped brands by letter
  const sortedGroupedBrands = Object.keys(groupedBrands)
    .sort()
    .reduce((obj, key) => {
      obj[key] = groupedBrands[key]
      return obj
    }, {} as Record<string, BrandWithDetails[]>)

  // Handle scroll to section when letter is clicked
  const handleLetterClick = (letter: string) => {
    setActiveLetter(letter)
    const section = document.getElementById(`section-${letter}`)
    if (section) {
      const headerOffset = 120
      const elementPosition = section.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    }
  }


  // Set up intersection observer for active letter highlighting
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const letter = entry.target.id.replace('section-', '')
            setActiveLetter(letter)
          }
        })
      },
      { threshold: 0.5, rootMargin: '-100px 0px -80% 0px' }
    )

    const sections = document.querySelectorAll('[id^="section-"]')
    sections.forEach(section => observer.observe(section))

    return () => {
      sections.forEach(section => observer.unobserve(section))
      observer.disconnect()
    }
  }, [searchQuery])

  // Generate collection data for structured data
  const collectionData = {
    name: "Shop by Brand | Cashback Deals",
    description: "Browse our extensive collection of brands offering exclusive cashback deals and discounts.",
    url: "/brands",
    brands: initialBrands.map(brand => ({
      name: brand.name,
      slug: brand.slug || brand.name.toLowerCase().replace(/\\s+/g, '-')
    })),
    brandCount: initialBrands.length,
    baseUrl: process.env.NEXT_PUBLIC_SITE_URL || (typeof window !== 'undefined' 
      ? `${window.location.protocol}//${window.location.host}` 
      : 'https://yourdomain.com')
  };

  return (
    <>
      <div className="relative flex flex-col min-h-screen">
      {/* Hero section with search */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary/5 via-secondary/5 to-background py-16 md:py-24"
      >
        <div className="container px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-3xl mx-auto text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Discover Brands
            </h1>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Browse through our extensive collection of brands offering exclusive deals and cashback.
            </p>
            
            {/* Search input */}
            <div className="max-w-xl mx-auto">
              <input
                type="text"
                placeholder="Search brands..."
                className="w-full px-4 py-3 rounded-lg border border-input bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Main content area */}
      <div className="relative flex-1">
        {/* Sticky alphabet navigation */}
        <div className="sticky top-[64px] z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border shadow-sm">
          <div className="container px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-center overflow-x-auto py-2 hide-scrollbar">
              <div className="flex space-x-1">
                {Object.keys(sortedGroupedBrands).map((letter) => (
                  <button
                    key={letter}
                    onClick={() => handleLetterClick(letter)}
                    className={`w-8 h-8 flex items-center justify-center rounded-full text-sm font-medium transition-colors ${
                      activeLetter === letter
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                    }`}
                  >
                    {letter}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Brands grid */}
        <main className="container px-4 sm:px-6 lg:px-8 py-8">
          {Object.entries(sortedGroupedBrands).length > 0 ? (
            <div className="space-y-12">
              {Object.entries(sortedGroupedBrands).map(([letter, brands]) => (
                <div key={letter} id={`section-${letter}`} className="py-12">
                  <h2 className="text-3xl font-bold text-foreground mb-4">{letter}</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    {brands.map((brand) => (
                      <Link key={brand.id} href={`/brands/${brand.slug}`}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 }}
                          className="bg-background rounded-lg shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="h-40 bg-secondary/10 relative flex items-center justify-center">
                            {brand.logoUrl ? (
                              <Image
                                src={brand.logoUrl}
                                alt={brand.name}
                                width={160}
                                height={160}
                                className="object-contain p-4"
                                style={{ width: 'auto', height: 'auto', maxHeight: '80%' }}
                                loading="lazy"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement
                                  target.src = `https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(
                                    brand.name
                                  )}`
                                }}
                              />
                            ) : (
                              <div className="text-2xl font-bold text-secondary/40">{brand.name}</div>
                            )}
                          </div>
                          <div className="p-6">
                            <h3 className="text-lg font-semibold text-primary mb-2">{brand.name}</h3>
                            {brand.categories && brand.categories.length > 0 && (
                              <p className="text-sm text-foreground/70 mb-4">
                                Categories: {brand.categories.map((c: any) => c.name).join(', ')}
                              </p>
                            )}
                            <motion.div whileHover={{ x: 5 }}>
                              <span className="inline-flex items-center gap-2 text-sm font-medium text-primary">
                                View Deals <ArrowRight className="h-4 w-4" />
                              </span>
                            </motion.div>
                          </div>
                        </motion.div>
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <h2 className="text-2xl font-semibold text-muted-foreground">
                No brands found
              </h2>
              <p className="mt-2 text-muted-foreground">
                We couldn't find any brands matching your criteria.
              </p>
            </div>
          )}
        </main>
      </div>
      </div>
    </>
  )
}
