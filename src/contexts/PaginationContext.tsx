'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface PaginationState {
  [key: string]: {
    currentPage: number;
    filters: Record<string, string>;
    lastUpdated: number;
  };
}

interface PaginationContextType {
  getPageState: (key: string) => { currentPage: number; filters: Record<string, string> };
  setPageState: (key: string, page: number, filters?: Record<string, string>) => void;
  clearPageState: (key: string) => void;
  navigateToPage: (key: string, page: number) => void;
}

const PaginationContext = createContext<PaginationContextType | undefined>(undefined);

const STORAGE_KEY = 'pagination-state';
const MAX_AGE = 60 * 60 * 1000; // 1 hour

export function PaginationProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<PaginationState>({});
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize from localStorage
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedState: PaginationState = JSON.parse(stored);
        
        // Clean up expired entries
        const now = Date.now();
        const cleanState: PaginationState = {};
        
        Object.entries(parsedState).forEach(([key, value]) => {
          if (now - value.lastUpdated < MAX_AGE) {
            cleanState[key] = value;
          }
        });
        
        setState(cleanState);
        
        // Save cleaned state back to localStorage
        if (Object.keys(cleanState).length !== Object.keys(parsedState).length) {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(cleanState));
        }
      }
    } catch (error) {
      console.warn('Failed to load pagination state:', error);
    } finally {
      setIsInitialized(true);
    }
  }, []);

  // Save to localStorage whenever state changes
  useEffect(() => {
    if (!isInitialized) return;
    
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save pagination state:', error);
    }
  }, [state, isInitialized]);

  const getPageState = (key: string) => {
    const pageState = state[key];
    if (pageState) {
      return {
        currentPage: pageState.currentPage,
        filters: pageState.filters
      };
    }
    
    // Fallback to URL parameters
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const pageFromUrl = parseInt(urlParams.get('page') || '1', 10);
      return {
        currentPage: pageFromUrl,
        filters: {}
      };
    }
    
    return {
      currentPage: 1,
      filters: {}
    };
  };

  const setPageState = (key: string, page: number, filters: Record<string, string> = {}) => {
    setState(prev => ({
      ...prev,
      [key]: {
        currentPage: page,
        filters,
        lastUpdated: Date.now()
      }
    }));
    
    // Update URL for bookmarking
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      
      if (page === 1) {
        url.searchParams.delete('page');
      } else {
        url.searchParams.set('page', page.toString());
      }
      
      // Update filters in URL
      Object.entries(filters).forEach(([filterKey, value]) => {
        if (value && value.trim()) {
          url.searchParams.set(filterKey, value);
        } else {
          url.searchParams.delete(filterKey);
        }
      });
      
      // Use replaceState to avoid cluttering browser history
      window.history.replaceState({}, '', url.toString());
    }
  };

  const clearPageState = (key: string) => {
    setState(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  };

  const navigateToPage = (key: string, page: number) => {
    const currentState = getPageState(key);
    setPageState(key, page, currentState.filters);
  };

  return (
    <PaginationContext.Provider value={{
      getPageState,
      setPageState,
      clearPageState,
      navigateToPage
    }}>
      {children}
    </PaginationContext.Provider>
  );
}

export function usePaginationContext() {
  const context = useContext(PaginationContext);
  if (context === undefined) {
    throw new Error('usePaginationContext must be used within a PaginationProvider');
  }
  return context;
}

/**
 * Hook for products page pagination
 */
export function useProductsPagination() {
  const { getPageState, setPageState, navigateToPage } = usePaginationContext();
  const key = 'products';
  
  const { currentPage, filters } = getPageState(key);
  
  const goToPage = (page: number) => {
    navigateToPage(key, page);
  };
  
  const updateFilters = (newFilters: Record<string, string>) => {
    setPageState(key, 1, newFilters); // Reset to page 1 when filters change
  };
  
  return {
    currentPage,
    filters,
    goToPage,
    updateFilters
  };
}
