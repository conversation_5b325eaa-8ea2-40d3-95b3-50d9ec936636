'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';

interface UsePaginationOptions {
  defaultPage?: number;
  pageSize?: number;
  basePath?: string;
}

interface UsePaginationReturn {
  currentPage: number;
  pageSize: number;
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  updateFilters: (filters: Record<string, string | undefined>) => void;
  clearFilters: () => void;
  getPageUrl: (page: number) => string;
}

/**
 * Custom hook for managing pagination state via URL parameters
 * 
 * This hook provides a consistent way to handle pagination across all listing pages.
 * It maintains pagination state in the URL, ensuring that users can bookmark,
 * share, and navigate back to specific pages while preserving their state.
 * 
 * Features:
 * - URL-based state persistence
 * - Browser back/forward navigation support
 * - Filter management alongside pagination
 * - Automatic page reset when filters change
 * - Type-safe parameter handling
 * 
 * @param options Configuration options for pagination
 * @returns Pagination state and navigation functions
 */
export function usePagination({
  defaultPage = 1,
  pageSize = 20,
  basePath = ''
}: UsePaginationOptions = {}): UsePaginationReturn {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current page from URL parameters
  const currentPage = parseInt(searchParams?.get('page') || defaultPage.toString(), 10);

  /**
   * Navigate to a specific page
   * Preserves all existing URL parameters except page
   */
  const goToPage = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    
    if (page === defaultPage) {
      // Remove page parameter if going to default page to keep URLs clean
      params.delete('page');
    } else {
      params.set('page', page.toString());
    }
    
    const url = basePath + (params.toString() ? `?${params.toString()}` : '');
    router.push(url);
  }, [router, searchParams, defaultPage, basePath]);

  /**
   * Navigate to the next page
   */
  const goToNextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [goToPage, currentPage]);

  /**
   * Navigate to the previous page
   */
  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  }, [goToPage, currentPage]);

  /**
   * Update filters and reset to first page
   * This is useful when applying new filters that might change the total number of results
   */
  const updateFilters = useCallback((filters: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    
    // Remove page parameter when updating filters to reset to first page
    params.delete('page');
    
    // Update filter parameters
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.trim()) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    
    const url = basePath + (params.toString() ? `?${params.toString()}` : '');
    router.push(url);
  }, [router, searchParams, basePath]);

  /**
   * Clear all filters and reset to first page
   */
  const clearFilters = useCallback(() => {
    router.push(basePath);
  }, [router, basePath]);

  /**
   * Get URL for a specific page (useful for generating links)
   */
  const getPageUrl = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '');
    
    if (page === defaultPage) {
      params.delete('page');
    } else {
      params.set('page', page.toString());
    }
    
    return basePath + (params.toString() ? `?${params.toString()}` : '');
  }, [searchParams, defaultPage, basePath]);

  return {
    currentPage,
    pageSize,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    updateFilters,
    clearFilters,
    getPageUrl
  };
}

/**
 * Hook specifically for products page pagination
 */
export function useProductsPagination() {
  return usePagination({
    defaultPage: 1,
    pageSize: 20,
    basePath: '/products'
  });
}

/**
 * Hook specifically for retailers page pagination
 */
export function useRetailersPagination() {
  return usePagination({
    defaultPage: 1,
    pageSize: 24,
    basePath: '/retailers'
  });
}

/**
 * Hook specifically for brands page pagination
 */
export function useBrandsPagination() {
  return usePagination({
    defaultPage: 1,
    pageSize: 24,
    basePath: '/brands'
  });
}

/**
 * Hook specifically for promotions page pagination
 */
export function usePromotionsPagination() {
  return usePagination({
    defaultPage: 1,
    pageSize: 20,
    basePath: '/promotions'
  });
}
