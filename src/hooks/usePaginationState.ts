'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface PaginationState {
  page: number;
  filters: Record<string, string>;
  timestamp: number;
}

interface UsePaginationStateOptions {
  storageKey: string;
  defaultPage?: number;
  maxAge?: number; // Maximum age in milliseconds
}

/**
 * Custom hook for managing pagination state with session storage persistence
 * 
 * This approach uses session storage to persist pagination state across navigation,
 * providing a better user experience than URL-only approaches.
 * 
 * Features:
 * - Session storage persistence (survives page navigation)
 * - Automatic cleanup of stale data
 * - Browser back/forward support
 * - Filter state management
 * - Fallback to URL parameters when available
 */
export function usePaginationState({
  storageKey,
  defaultPage = 1,
  maxAge = 30 * 60 * 1000 // 30 minutes default
}: UsePaginationStateOptions) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(defaultPage);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize state from session storage or URL
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      // First, try to get state from session storage
      const stored = sessionStorage.getItem(storageKey);
      if (stored) {
        const parsedState: PaginationState = JSON.parse(stored);
        
        // Check if the stored state is not too old
        if (Date.now() - parsedState.timestamp < maxAge) {
          setCurrentPage(parsedState.page);
          setIsInitialized(true);
          return;
        } else {
          // Clean up stale data
          sessionStorage.removeItem(storageKey);
        }
      }

      // Fallback to URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const pageFromUrl = parseInt(urlParams.get('page') || defaultPage.toString(), 10);
      setCurrentPage(pageFromUrl);
      
      // Store the initial state
      const initialState: PaginationState = {
        page: pageFromUrl,
        filters: {},
        timestamp: Date.now()
      };
      sessionStorage.setItem(storageKey, JSON.stringify(initialState));
      
    } catch (error) {
      console.warn('Failed to initialize pagination state:', error);
      setCurrentPage(defaultPage);
    } finally {
      setIsInitialized(true);
    }
  }, [storageKey, defaultPage, maxAge]);

  // Save state to session storage
  const saveState = useCallback((page: number, filters: Record<string, string> = {}) => {
    if (typeof window === 'undefined') return;

    try {
      const state: PaginationState = {
        page,
        filters,
        timestamp: Date.now()
      };
      sessionStorage.setItem(storageKey, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save pagination state:', error);
    }
  }, [storageKey]);

  // Navigate to a specific page
  const goToPage = useCallback((page: number) => {
    setCurrentPage(page);
    saveState(page);
    
    // Update URL for bookmarking and sharing
    const url = new URL(window.location.href);
    if (page === defaultPage) {
      url.searchParams.delete('page');
    } else {
      url.searchParams.set('page', page.toString());
    }
    
    // Use replace to avoid cluttering browser history
    window.history.replaceState({}, '', url.toString());
  }, [defaultPage, saveState]);

  // Navigate to next page
  const goToNextPage = useCallback(() => {
    goToPage(currentPage + 1);
  }, [currentPage, goToPage]);

  // Navigate to previous page
  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  }, [currentPage, goToPage]);

  // Update filters and optionally reset page
  const updateFilters = useCallback((filters: Record<string, string>, resetPage = true) => {
    const newPage = resetPage ? defaultPage : currentPage;
    setCurrentPage(newPage);
    saveState(newPage, filters);
    
    // Update URL with filters
    const url = new URL(window.location.href);
    
    // Clear existing filter parameters
    const filterKeys = ['search', 'brand_id', 'category_id', 'promotion_id'];
    filterKeys.forEach(key => url.searchParams.delete(key));
    
    // Add new filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.trim()) {
        url.searchParams.set(key, value);
      }
    });
    
    // Update page parameter
    if (newPage === defaultPage) {
      url.searchParams.delete('page');
    } else {
      url.searchParams.set('page', newPage.toString());
    }
    
    window.history.replaceState({}, '', url.toString());
  }, [currentPage, defaultPage, saveState]);

  // Clear all state
  const clearState = useCallback(() => {
    if (typeof window === 'undefined') return;
    
    try {
      sessionStorage.removeItem(storageKey);
      setCurrentPage(defaultPage);
      
      // Clear URL parameters
      const url = new URL(window.location.href);
      url.search = '';
      window.history.replaceState({}, '', url.toString());
    } catch (error) {
      console.warn('Failed to clear pagination state:', error);
    }
  }, [storageKey, defaultPage]);

  // Handle browser back/forward navigation
  useEffect(() => {
    if (!isInitialized) return;

    const handlePopState = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const pageFromUrl = parseInt(urlParams.get('page') || defaultPage.toString(), 10);
      setCurrentPage(pageFromUrl);
      saveState(pageFromUrl);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [isInitialized, defaultPage, saveState]);

  return {
    currentPage,
    isInitialized,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    updateFilters,
    clearState
  };
}

/**
 * Hook specifically for products page pagination
 */
export function useProductsPaginationState() {
  return usePaginationState({
    storageKey: 'products-pagination-state',
    defaultPage: 1,
    maxAge: 30 * 60 * 1000 // 30 minutes
  });
}
