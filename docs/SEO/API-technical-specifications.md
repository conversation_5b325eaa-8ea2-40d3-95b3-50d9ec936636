# RebateRay Platform - API Technical Specifications

## Table of Contents
1. [Platform Overview](#platform-overview)
2. [Architecture Overview](#architecture-overview)
3. [Database Schema](#database-schema)
4. [API Endpoints](#api-endpoints)
5. [Data Layer Architecture](#data-layer-architecture)
6. [Security & Authentication](#security--authentication)
7. [Performance & Caching](#performance--caching)
8. [Frontend Architecture](#frontend-architecture)
9. [Deployment & Infrastructure](#deployment--infrastructure)
10. [Development Guidelines](#development-guidelines)

## Platform Overview

### Technology Stack
- **Framework**: Next.js 15.1.4 (App Router)
- **Runtime**: React 19.0.0
- **Database**: Supabase (PostgreSQL)
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: TanStack React Query
- **Animation**: Framer Motion
- **Deployment**: Amazon Amplify
- **Language**: TypeScript

### Core Business Logic
RebateRay is a cashback deals platform that connects consumers with retailers through promotional offers. The platform manages:
- Product catalog with retailer offers
- Brand partnerships and promotions
- Cashback claim processing
- User purchase tracking
- Retailer affiliate relationships

## Architecture Overview

### Application Structure
```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # Root layout (SERVER)
│   ├── page.tsx           # Homepage (CLIENT)
│   ├── products/          # Product pages (CLIENT)
│   ├── brands/            # Brand pages (CLIENT)
│   ├── search/            # Search functionality (CLIENT)
│   └── api/               # API routes (SERVER)
├── components/            # UI components
├── lib/                   # Utilities and configurations
│   ├── data/             # Server-side data layer
│   ├── supabase/         # Database clients
│   └── cache/            # Caching utilities
└── types/                # TypeScript definitions
```

### Rendering Strategy
- **Server Components**: API routes, data layer functions
- **Client Components**: Interactive UI, state management
- **Hybrid Approach**: Server-side initial data + client-side updates

## Database Schema

### Core Tables

#### Products Table
```sql
products (
  id: uuid PRIMARY KEY,
  name: varchar(150) NOT NULL,
  slug: varchar(150) NOT NULL UNIQUE,
  brand_id: uuid REFERENCES brands(id),
  category_id: uuid REFERENCES categories(id),
  promotion_id: uuid REFERENCES promotions(id),
  description: text,
  specifications: jsonb,
  images: text[],
  status: varchar(20) DEFAULT 'active',
  is_featured: boolean DEFAULT false,
  is_sponsored: boolean DEFAULT false,
  cashback_amount: numeric,
  model_number: text NOT NULL,
  search_vector: tsvector,
  created_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  version: bigint DEFAULT 1
)
```

#### Brands Table
```sql
brands (
  id: uuid PRIMARY KEY,
  name: varchar(100) NOT NULL,
  slug: varchar(100) NOT NULL UNIQUE,
  logo_url: varchar(255),
  description: text,
  featured: boolean DEFAULT false,
  sponsored: boolean DEFAULT false,
  search_vector: tsvector,
  created_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  version: bigint DEFAULT 1
)
```

#### Retailers Table
```sql
retailers (
  id: uuid PRIMARY KEY,
  name: varchar(150) NOT NULL,
  slug: varchar(150) NOT NULL UNIQUE,
  logo_url: varchar(255),
  website_url: varchar(255),
  status: varchar(20) DEFAULT 'active',
  featured: boolean DEFAULT false,
  sponsored: boolean DEFAULT false,
  claim_period: varchar(50),
  api_key_hash: varchar(255),
  api_secret_hash: varchar(255),
  created_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  version: bigint DEFAULT 1
)
```

#### Product Retailer Offers Table
```sql
product_retailer_offers (
  id: uuid PRIMARY KEY,
  product_id: uuid NOT NULL REFERENCES products(id),
  retailer_id: uuid NOT NULL REFERENCES retailers(id),
  price: numeric NOT NULL,
  stock_status: text DEFAULT 'in_stock',
  url: varchar,
  created_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  version: bigint DEFAULT 1
)
```

#### Promotions Table
```sql
promotions (
  id: uuid PRIMARY KEY,
  brand_id: uuid REFERENCES brands(id),
  category_id: uuid REFERENCES categories(id),
  title: varchar(150) NOT NULL,
  description: text,
  max_cashback_amount: numeric NOT NULL,
  purchase_start_date: date NOT NULL,
  purchase_end_date: date NOT NULL,
  claim_start_offset_days: integer NOT NULL,
  claim_window_days: integer NOT NULL,
  terms_url: varchar(255),
  terms_description: text,
  status: promotion_status_type DEFAULT 'draft',
  is_featured: boolean DEFAULT false,
  last_validated_at: timestamp,
  created_at: timestamp DEFAULT CURRENT_TIMESTAMP,
  version: bigint DEFAULT 1
)
```

### Relationship Mapping
- Products → Brands (many-to-one)
- Products → Categories (many-to-one)
- Products → Promotions (many-to-one)
- Products → Retailers (many-to-many via product_retailer_offers)
- Users → Products (many-to-many via user_purchases, user_favorites)
- Users → Cashback Claims (one-to-many via user_cashback_claims)

## API Endpoints

### Products API

#### GET /api/products
**Purpose**: Retrieve paginated list of products with filtering
**Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 50)
- `brand_id`: Filter by brand UUID
- `category_id`: Filter by category UUID
- `featured`: Filter featured products (boolean)
- `sponsored`: Filter sponsored products (boolean)
- `sort`: Sort order (name_asc, name_desc, price_asc, price_desc, newest, featured)

**Response Format**:
```typescript
{
  data: {
    products: TransformedProduct[],
    pagination: {
      page: number,
      limit: number,
      total: number,
      totalPages: number
    }
  },
  error: string | null
}
```

#### GET /api/products/[id]
**Purpose**: Retrieve single product with detailed information
**Parameters**:
- `id`: Product UUID or slug
- `include_similar`: Include similar products (boolean)

**Response Format**:
```typescript
{
  data: {
    product: TransformedProduct,
    similar_products?: TransformedProduct[]
  },
  error: string | null
}
```

#### GET /api/products/featured
**Purpose**: Retrieve featured products for homepage
**Parameters**:
- `limit`: Number of products (default: 10, max: 20)

### Brands API

#### GET /api/brands
**Purpose**: Retrieve paginated list of brands
**Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 50)
- `featured`: Filter featured brands (boolean)

#### GET /api/brands/[id]
**Purpose**: Retrieve brand details with associated products
**Parameters**:
- `id`: Brand UUID or slug
- `include_products`: Include brand products (boolean, default: true)
- `products_limit`: Limit for products (default: 8)

### Retailers API

#### GET /api/retailers
**Purpose**: Retrieve paginated list of retailers
**Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 50)
- `featured`: Filter featured retailers (boolean)

#### GET /api/retailers/[id]
**Purpose**: Retrieve retailer details with featured products
**Parameters**:
- `id`: Retailer UUID or slug

#### GET /api/retailers/featured
**Purpose**: Retrieve featured retailers for homepage
**Parameters**:
- `limit`: Number of retailers (default: 10, max: 50)

### Search API

#### GET /api/search
**Purpose**: Search products with advanced filtering
**Parameters**:
- `q`: Search query string
- `category`: Category ID filter
- `brand`: Brand name filter
- `sort`: Sort order (relevance, price_asc, price_desc, newest, featured)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 50)

**Rate Limiting**: 20 requests per minute per IP

### Contact API

#### POST /api/contact
**Purpose**: Handle contact form submissions
**Request Body**:
```typescript
{
  name: string,
  email: string,
  subject: string,
  message: string
}
```

**Rate Limiting**: 5 requests per 5 minutes per IP
**Security**: Input validation, sanitization, email verification

## Data Layer Architecture

### Server-Side Data Functions
Located in `src/lib/data/`, the data layer provides:

#### Products Module (`src/lib/data/products.ts`)
- `getProduct(id: string)`: Get single product by ID
- `getProducts(filters, page, limit)`: Get paginated products
- `getFeaturedProducts(limit)`: Get featured products
- `getProductBySlug(slug)`: Get product by SEO-friendly slug
- `getProductWithSimilar(id)`: Get product with similar items

#### Brands Module (`src/lib/data/brands.ts`)
- `getBrand(id: string)`: Get single brand
- `getBrands(filters, page, limit)`: Get paginated brands
- `getBrandWithDetails(id)`: Get brand with products and promotions
- `getBrandBySlug(slug)`: Get brand by slug

#### Retailers Module (`src/lib/data/retailers.ts`)
- `getRetailer(id: string)`: Get single retailer
- `getRetailers(filters, page, limit)`: Get paginated retailers
- `getFeaturedRetailers(limit)`: Get featured retailers
- `getRetailerWithProducts(id)`: Get retailer with featured products

#### Search Module (`src/lib/data/search.ts`)
- `searchProducts(filters, page, limit)`: Advanced product search
- Full-text search using PostgreSQL tsvector
- Relevance scoring and ranking

### Data Transformation
All data functions return transformed objects that:
- Normalize database relationships
- Calculate derived fields (e.g., best prices)
- Format data for frontend consumption
- Include SEO-optimized URLs and metadata

### Caching Strategy
- **Function-level caching**: Using Next.js `unstable_cache`
- **Cache durations**: 
  - Products: 30 minutes
  - Brands: 1 hour
  - Featured content: 5 minutes
  - Search results: 15 minutes
- **Cache invalidation**: Automatic on data updates
- **Cache tags**: For selective invalidation

## Security & Authentication

### Environment Variables
```bash
# Server-side only (secure)
SUPABASE_SERVICE_ROLE_KEY=xxx
EMAIL_USER=xxx
EMAIL_PASSWORD=xxx

# Client-side (public)
NEXT_PUBLIC_SUPABASE_URL=xxx
NEXT_PUBLIC_SUPABASE_ANON_KEY=xxx
```

### Database Security
- **Row Level Security (RLS)**: Enabled on user-specific tables
- **Service Role Access**: Server-side operations use service role key
- **Anonymous Access**: Client-side uses anonymous key with RLS
- **API Key Management**: Retailer API keys stored as hashes

### Rate Limiting
Comprehensive rate limiting implemented across all endpoints:
- **Default**: 60 requests/minute (fallback)
- **Search**: 20 requests/minute (search queries and suggestions)
- **Contact**: 5 requests/5 minutes (contact form submissions)
- **Products**: 30 requests/minute (product listings and details)
- **Brands**: 40 requests/minute (brand listings and details)
- **Retailers**: 35 requests/minute (retailer listings, details, and featured)

#### Rate Limiting Features
- **Per-IP tracking**: Individual IP address monitoring
- **Sliding window**: Time-based request counting
- **Graceful degradation**: Proper HTTP 429 responses
- **Retry-After headers**: Client guidance for retry timing
- **Memory cleanup**: Automatic cleanup of expired entries
- **Configurable limits**: Easy adjustment per endpoint type

#### Rate Limiting Implementation Details
```typescript
// Rate limiting configuration
const rateLimits = {
  default: { maxRequests: 60, windowSizeInSeconds: 60 },
  search: { maxRequests: 20, windowSizeInSeconds: 60 },
  contact: { maxRequests: 5, windowSizeInSeconds: 300 },
  product: { maxRequests: 30, windowSizeInSeconds: 60 },
  brands: { maxRequests: 40, windowSizeInSeconds: 60 },
  retailers: { maxRequests: 35, windowSizeInSeconds: 60 }
}

// Rate limit response headers
{
  'X-RateLimit-Limit': '30',
  'X-RateLimit-Remaining': '0',
  'X-RateLimit-Reset': '1640995200',
  'Retry-After': '45'
}
```

#### Endpoint-Specific Rate Limits
| Endpoint | Rate Limit | Reasoning |
|----------|------------|-----------|
| **Products API** (`/api/products`) | 30 requests/minute | Product catalog browsing |
| **Product Detail** (`/api/products/[id]`) | 30 requests/minute | Individual product views |
| **Brands API** (`/api/brands`) | 40 requests/minute | Brand browsing is common |
| **Brand Detail** (`/api/brands/[id]`) | 40 requests/minute | Brand detail exploration |
| **Retailers API** (`/api/retailers`) | 35 requests/minute | Retailer discovery usage |
| **Retailer Detail** (`/api/retailers/[id]`) | 35 requests/minute | Retailer information access |
| **Featured Retailers** (`/api/retailers/featured`) | 35 requests/minute | Homepage/landing page usage |
| **Search API** (`/api/search`) | 20 requests/minute | Prevents search abuse |
| **Search Suggestions** (`/api/search/suggestions`) | 20 requests/minute | Autocomplete protection |
| **Contact API** (`/api/contact`) | 5 requests/5 minutes | Spam prevention |

#### Rate Limiting Strategy
- **High-Frequency Endpoints**: 30-40 requests/minute for browsing APIs
- **Medium-Frequency Endpoints**: 20 requests/minute for search functionality
- **Low-Frequency Endpoints**: 5 requests/5 minutes for form submissions
- **DoS Protection**: Prevents server overwhelming and resource exhaustion
- **Fair Usage**: Ensures equitable access for all legitimate users

### CORS Configuration
- **Origins**: Configurable allowed origins
- **Methods**: GET, POST, OPTIONS
- **Headers**: Content-Type, Authorization

## Performance & Caching

### Server-Side Caching
```typescript
// Example cached function
export const getProducts = createCachedFunction(
  _getProducts,
  {
    key: 'getProducts',
    revalidate: CACHE_DURATIONS.MEDIUM, // 30 minutes
    tags: [CACHE_TAGS.PRODUCTS],
  }
)
```

### Client-Side Caching
- **React Query**: Aggressive caching with stale-while-revalidate
- **Stale Time**: 30 minutes for most data
- **Garbage Collection**: 60 minutes
- **Background Refetch**: Disabled for better performance

### Image Optimization
- **Next.js Image**: Automatic optimization and lazy loading
- **Remote Patterns**: Configured for Supabase, AWS, CloudFront
- **Formats**: WebP with fallbacks

### Database Optimization
- **Indexes**: On frequently queried columns (slug, status, featured)
- **Full-text Search**: tsvector indexes for search functionality
- **Connection Pooling**: Supabase handles connection management

## Frontend Architecture

### Component Structure
- **UI Components**: shadcn/ui based design system
- **Feature Components**: Business logic components
- **Layout Components**: Header, footer, navigation
- **Page Components**: Route-specific components

### State Management
- **Server State**: TanStack React Query
- **Client State**: React hooks (useState, useReducer)
- **Global State**: Context API for theme, user preferences

### Styling System
- **Tailwind CSS**: Utility-first CSS framework
- **Design Tokens**: Consistent color, spacing, typography
- **Component Variants**: Using class-variance-authority (cva)
- **Responsive Design**: Mobile-first approach

### SEO Optimization
- **Metadata**: Dynamic meta tags per page
- **Structured Data**: JSON-LD for products, brands, offers
- **Sitemap**: Auto-generated from database
- **Open Graph**: Social media optimization

## Deployment & Infrastructure

### Amazon Amplify Deployment
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Node Version**: 18.x
- **Environment Variables**: Configured in Amplify console

### Database Infrastructure
- **Supabase**: Managed PostgreSQL
- **Backups**: Automated daily backups
- **Scaling**: Auto-scaling based on usage
- **Monitoring**: Built-in performance monitoring

### CDN & Assets
- **Static Assets**: Served via Amplify CDN
- **Images**: Supabase storage with CDN
- **Caching**: Edge caching for static content

## Development Guidelines

### Code Organization
- **Feature-based**: Group related functionality
- **Separation of Concerns**: Clear boundaries between layers
- **Type Safety**: Comprehensive TypeScript usage
- **Error Handling**: Consistent error patterns

### API Design Principles
- **RESTful**: Standard HTTP methods and status codes
- **Consistent Responses**: Standardized response format
- **Pagination**: Cursor-based for large datasets
- **Versioning**: URL-based versioning strategy

### Testing Strategy
- **Unit Tests**: Jest for utility functions
- **Integration Tests**: API route testing
- **E2E Tests**: Playwright for critical user flows
- **Performance Tests**: Lighthouse CI integration

### Monitoring & Logging
- **Error Tracking**: Console logging with structured format
- **Performance Monitoring**: Core Web Vitals tracking
- **API Monitoring**: Response time and error rate tracking
- **User Analytics**: Privacy-focused analytics

## Data Types & Interfaces

### Core Data Types

> **Important**: All API responses use camelCase field naming for consistency with JavaScript/TypeScript conventions. The database schema uses snake_case, but data transformation functions convert all field names to camelCase before returning to clients.

#### TransformedProduct
```typescript
interface TransformedProduct {
  id: string
  name: string
  slug: string
  description: string | null
  images: string[] | null
  status: string | null
  isFeatured: boolean | null
  isSponsored: boolean | null
  cashbackAmount: number | null
  modelNumber: string
  createdAt: string
  updatedAt: string

  // Related entities
  brand: {
    id: string
    name: string
    slug: string
    logoUrl: string | null
  } | null

  category: {
    id: string
    name: string
    slug: string
  } | null

  promotion: {
    id: string
    title: string
    description: string | null
    maxCashbackAmount: number
    purchaseStartDate: string
    purchaseEndDate: string
    status: string
  } | null

  // Retailer offers
  retailerOffers: TransformedRetailerOffer[]

  // Computed fields
  bestPrice: number | null
  availableRetailersCount: number
  seoUrl: string
}
```

#### TransformedBrand
```typescript
interface TransformedBrand {
  id: string
  name: string
  slug: string
  logoUrl: string | null
  description: string | null
  featured: boolean | null
  sponsored: boolean | null
  createdAt: string
  updatedAt: string

  // Computed fields
  productsCount: number
  activePromotionsCount: number
  seoUrl: string
}
```

#### TransformedRetailer
```typescript
interface TransformedRetailer {
  id: string
  name: string
  slug: string
  logoUrl: string | null
  websiteUrl: string | null
  status: string | null
  featured: boolean | null
  sponsored: boolean | null
  claimPeriod: string | null
  createdAt: string

  // Computed fields
  activeOffersCount: number
  seoUrl: string
}
```

#### ApiResponse
```typescript
interface ApiResponse<T> {
  data: T | null
  error: string | null
  meta?: {
    timestamp: string
    request_id: string
    cache_status: 'hit' | 'miss' | 'stale'
  }
}
```

#### PaginatedResponse
```typescript
interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPreviousPage: boolean
  }
  error: string | null
}
```

### Filter Interfaces

#### ProductFilters
```typescript
interface ProductFilters {
  brandId?: string
  categoryId?: string
  promotionId?: string
  status?: string
  isFeatured?: boolean
  isSponsored?: boolean
  minPrice?: number
  maxPrice?: number
  hasCashback?: boolean
}
```

#### SearchFilters
```typescript
interface SearchFilters {
  query?: string
  category?: string
  brand?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'newest' | 'featured'
  hasOffers?: boolean
}
```

## Error Handling & Status Codes

### HTTP Status Codes
- **200 OK**: Successful request
- **400 Bad Request**: Invalid request parameters
- **404 Not Found**: Resource not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error
- **503 Service Unavailable**: Service temporarily unavailable

### Error Response Format
```typescript
interface ErrorResponse {
  error: string
  message: string
  code?: string
  details?: Record<string, any>
  timestamp: string
  request_id: string
}
```

### Common Error Scenarios
1. **Invalid UUID Format**: When product/brand/retailer ID is malformed
2. **Resource Not Found**: When requested entity doesn't exist
3. **Rate Limit Exceeded**: When API limits are hit
4. **Database Connection**: When database is unavailable
5. **Validation Errors**: When request parameters are invalid

## Business Logic & Rules

### Product Availability Rules
- Products must have `status = 'active'` to appear in public APIs
- Featured products are prioritized in search results
- Sponsored products appear at the top of relevant listings
- Products without retailer offers are still displayed but marked as unavailable

### Cashback Calculation Logic
```typescript
// Cashback amount priority:
// 1. Product-specific cashback_amount
// 2. Promotion max_cashback_amount
// 3. Default brand/category cashback rates
function calculateCashback(product: Product): number {
  if (product.cashback_amount) {
    return product.cashback_amount
  }

  if (product.promotion?.max_cashback_amount) {
    return product.promotion.max_cashback_amount
  }

  // Fallback to default rates
  return 0
}
```

### Promotion Validity Rules
- Promotions must be within purchase date range
- Claim window starts after purchase_start_date + claim_start_offset_days
- Claims must be submitted within claim_window_days
- Promotion status must be 'active' for public display

### Search Ranking Algorithm
1. **Exact Name Match**: Highest priority
2. **Partial Name Match**: High priority
3. **Description Match**: Medium priority
4. **Brand Match**: Medium priority
5. **Category Match**: Lower priority
6. **Featured Status**: Boost ranking
7. **Sponsored Status**: Additional boost

## Integration Points

### External Services

#### Email Service (Nodemailer)
```typescript
// Contact form email configuration
const transporter = nodemailer.createTransporter({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
})
```

#### Supabase Storage
- **Product Images**: Stored in `products` bucket
- **Brand Logos**: Stored in `brands` bucket
- **Retailer Logos**: Stored in `retailers` bucket
- **CDN**: Automatic CDN distribution
- **Access Control**: Public read, authenticated write

### Third-Party APIs
- **Retailer APIs**: For real-time price and stock updates
- **Affiliate Networks**: For tracking and commission management
- **Analytics Services**: For user behavior tracking
- **Payment Processors**: For cashback disbursement

## Performance Benchmarks

### API Response Times (Target)
- **Product List**: < 200ms
- **Product Detail**: < 150ms
- **Search**: < 300ms
- **Brand List**: < 200ms
- **Featured Content**: < 100ms

### Database Query Optimization
- **Indexes**: All foreign keys and frequently filtered columns
- **Query Limits**: Maximum 50 items per page
- **Connection Pooling**: Managed by Supabase
- **Read Replicas**: For read-heavy operations

### Caching Performance
- **Cache Hit Ratio**: Target > 80%
- **Cache Invalidation**: < 5 seconds for critical updates
- **Memory Usage**: Monitor for cache size limits

## Security Considerations

### Input Sanitization & Validation

#### Current Security Status
- ✅ **Contact API**: Comprehensive validation and sanitization
- ✅ **Search API**: Enhanced with query validation and parameter sanitization
- ✅ **Products API**: Full input sanitization and UUID validation implemented
- ✅ **Brands API**: Comprehensive input validation and sanitization implemented
- ✅ **Retailers API**: Complete parameter validation and sanitization implemented
- ✅ **Product Detail API**: Enhanced with ID validation and sanitization
- ✅ **Search Suggestions API**: Query validation and sanitization implemented
- ✅ **All Detail APIs**: UUID/slug validation with proper sanitization

#### Implemented Security Measures
```typescript
// Centralized input sanitization utilities
export const sanitizeString = (input: string, maxLength = 255): string => {
  return input
    .trim()
    .replace(/[<>\"'&]/g, '') // Remove HTML/XML dangerous characters
    .replace(/[\x00-\x1f\x7f-\x9f]/g, '') // Remove control characters
    .substring(0, maxLength);
};

// Parameter validation for IDs and slugs
export const validateIdParameter = (id: string) => {
  const sanitized = sanitizeString(id, 150);
  const isUUID = isValidUUID(sanitized);
  const isValidSlug = /^[a-zA-Z0-9_-]+$/.test(sanitized);

  return { isValid: isUUID || isValidSlug, sanitized, isUUID };
};
```

#### Vulnerability Prevention
- **XSS Protection**: HTML/XML character removal and encoding across all endpoints
- **SQL Injection**: Prevented by Supabase parameterized queries
- **Parameter Pollution**: Length limits and type validation on all parameters
- **Control Character Injection**: Removal of control characters from all inputs
- **Script Injection**: Detection and blocking of suspicious patterns in search queries
- **UUID Validation**: Strict format validation for all ID parameters
- **Boolean Parameter Validation**: Explicit true/false validation
- **Enum Validation**: Whitelist validation for status and sort parameters
- **Input Length Limits**: Maximum length enforcement on all string inputs

#### Complete Input Sanitization Coverage
```typescript
// Example: Products API parameter validation
if (sanitizedFilters.brand_id) {
  if (isValidUUID(sanitizedFilters.brand_id)) {
    filters.brand_id = sanitizedFilters.brand_id
  } else {
    return NextResponse.json({
      error: 'Invalid brand_id format. Must be a valid UUID.'
    }, { status: 400 })
  }
}

// Example: Search query validation with suspicious pattern detection
const suspiciousPatterns = [/script/i, /javascript/i, /eval\(/i]
const hasSuspiciousContent = suspiciousPatterns.some(pattern =>
  pattern.test(sanitized)
)
```

#### Security Implementation Status
| Endpoint | Input Sanitization | Parameter Validation | Rate Limiting |
|----------|-------------------|---------------------|---------------|
| **Products API** | ✅ Complete | ✅ UUID, status, boolean validation | ✅ 30/min |
| **Product Detail** | ✅ Complete | ✅ Enhanced ID validation | ✅ 30/min |
| **Brands API** | ✅ Complete | ✅ Boolean + search validation | ✅ 40/min |
| **Brand Detail** | ✅ Complete | ✅ Enhanced ID validation | ✅ 40/min |
| **Retailers API** | ✅ Complete | ✅ Status whitelist + boolean | ✅ 35/min |
| **Retailer Detail** | ✅ Complete | ✅ Enhanced ID validation | ✅ 35/min |
| **Search API** | ✅ Complete | ✅ Query sanitization + pattern detection | ✅ 20/min |
| **Search Suggestions** | ✅ Complete | ✅ Full query validation | ✅ 20/min |
| **Contact API** | ✅ Complete | ✅ Comprehensive form validation | ✅ 5/5min |

#### Centralized Security Functions
All endpoints now use centralized security utilities from `src/lib/utils.ts`:
- `sanitizeString()`: Removes dangerous characters and enforces length limits
- `validateIdParameter()`: Validates UUIDs and slugs with sanitization
- `validatePaginationParams()`: Enforces pagination bounds and limits
- `validateSearchQuery()`: Detects suspicious patterns and sanitizes queries
- `validateFilterParams()`: Sanitizes all filter parameters consistently

### Data Protection
- **PII Handling**: User data encrypted at rest
- **API Keys**: Stored as hashes, never in plaintext
- **Session Management**: JWT tokens with expiration
- **Input Validation**: Comprehensive sanitization implemented

### Access Control
- **Public APIs**: Read-only access to active content
- **Admin APIs**: Require authentication and authorization
- **Rate Limiting**: Per-IP and per-user limits
- **CORS**: Restricted to allowed origins

### Compliance
- **GDPR**: User data deletion and export capabilities
- **CCPA**: California privacy compliance
- **PCI DSS**: For payment processing (if applicable)

## Security Implementation Summary

### Complete Security Coverage Achieved
The RebateRay platform now implements comprehensive security measures across all API endpoints:

#### ✅ **Input Sanitization (100% Coverage)**
- **All string inputs** sanitized to remove dangerous characters
- **Length limits** enforced on all parameters
- **Control character removal** from all user inputs
- **Suspicious pattern detection** in search queries
- **Centralized validation functions** for consistency

#### ✅ **Parameter Validation (100% Coverage)**
- **UUID format validation** for all ID parameters
- **Boolean parameter validation** with explicit true/false checking
- **Enum validation** with whitelist checking for status/sort parameters
- **Pagination bounds** enforcement with reasonable limits
- **Type safety** with comprehensive TypeScript validation

#### ✅ **Rate Limiting (100% Coverage)**
- **All endpoints protected** with appropriate rate limits
- **Per-IP tracking** with sliding window implementation
- **Graceful degradation** with proper HTTP 429 responses
- **Client guidance** with Retry-After headers
- **Memory management** with automatic cleanup

#### ✅ **SQL Injection Prevention**
- **Supabase parameterized queries** prevent SQL injection
- **No direct string interpolation** in database queries
- **Server-side data layer** isolation from user input
- **Service role key protection** for database access

#### ✅ **XSS Protection**
- **HTML/XML character removal** from all inputs
- **Script pattern detection** in search functionality
- **Output encoding** where necessary
- **Content-Type headers** properly set

### Security Benefits Delivered
1. **🛡️ Attack Prevention**: Protection against common web vulnerabilities
2. **⚡ Performance Protection**: Rate limiting prevents resource exhaustion
3. **🔒 Data Integrity**: Input validation ensures clean data storage
4. **📊 Monitoring Ready**: Structured logging for security monitoring
5. **🚀 Scalable Security**: Centralized functions for easy maintenance

### Security Monitoring & Maintenance
- **Structured logging** for all security events
- **Rate limit violation tracking** with IP monitoring
- **Input validation failure logging** for threat detection
- **Performance impact monitoring** for security overhead
- **Regular security review** processes established

## Monitoring & Observability

### Key Metrics
- **API Response Times**: P50, P95, P99 percentiles
- **Error Rates**: By endpoint and error type
- **Cache Performance**: Hit rates and invalidation frequency
- **Database Performance**: Query times and connection usage
- **User Engagement**: Page views, conversion rates

### Alerting Thresholds
- **Response Time**: > 500ms for 5 minutes
- **Error Rate**: > 5% for 2 minutes
- **Database Connections**: > 80% utilization
- **Cache Miss Rate**: > 50% for 10 minutes

### Logging Strategy
```typescript
// Structured logging format
{
  timestamp: "2024-01-15T10:30:00Z",
  level: "info",
  service: "api",
  endpoint: "/api/products",
  method: "GET",
  status_code: 200,
  response_time_ms: 145,
  user_id: "optional",
  request_id: "req_123456",
  metadata: {
    cache_status: "hit",
    query_params: {...}
  }
}
```

---

*This document serves as the comprehensive technical reference for the RebateRay platform. It should be updated as the system evolves and new features are added.*
